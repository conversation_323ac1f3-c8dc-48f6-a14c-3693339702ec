import React, { useState, useEffect } from 'react';
import { EmpreendimentoCreate, FaseEmpreendimentoEnum, ItemEmpreendimento, TipoEmpreendimento, TipoParteEmpreendimento } from '../../types/empreendimento';
import { ItemEmpreendimentoService, TipoParteEmpreendimentoService } from '../../services/empreendimentoService';
import GerenciarItensEmpreendimento from './GerenciarItensEmpreendimento';
import GerenciarTiposParteEmpreendimento from './GerenciarTiposParteEmpreendimento';
import TipoParteSelector from './componentes/tipoparteselector';
import { Building, Home, Plus, X } from 'lucide-react';
import Select from 'react-select';
import ItensEmpreendimentoSelector from './componentes/ItensEmpreendimentoSelector';

// --- INTERFACES ATUALIZADAS PARA A NOVA LÓGICA ---
interface Unidade {
    nome?: string;
    torre: number; // 1, 2, ... para torres; 0 para unidades sem torre
    tipo_parte_empreendimento?: (string | number)[];
    area_privativa_m2?: string | number;
    valor_imovel?: string | number;
    was_lancamento?: boolean;
    ano_disponibilidade?: number;
    mes_disponibilidade?: number;
    origem_recurso?: number;
    forma_pagamento?: string;
    atributos_extras?: Record<string, any>;
    disponibilidade?: number;
    quantidade_garagem?: number;
    mcmv?: boolean; 
    tipo_produto?: number; 
}

interface Torre {
    nome: string;
    unidades: Unidade[][];
}

interface EmpreendimentoFormEspecificoProps {
    basicData: EmpreendimentoCreate;
    onSave: (data: any) => void;
    onBack?: (specificData?: any) => void;
    initialSpecificData?: any;
}
const tiposDeEmpreendimento = [
    { value: 1, label: 'Lote' },
    { value: 2, label: 'Apartamento' },
    { value: 3, label: 'Comercial' },
    { value: 4, label: 'Casa' },
    { value: 5, label: 'Compacto' },
];
// Estilos (sem alterações)
const inputStyle = "block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm";
const primaryButtonStyle = "px-6 py-2 bg-indigo-600 text-white font-semibold rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors disabled:opacity-50";
const secondaryButtonStyle = "px-6 py-2 bg-white text-gray-700 font-semibold border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors";
const successButtonStyle = "px-4 py-2 bg-emerald-600 text-white font-semibold rounded-md shadow-sm hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors";
const dangerButtonStyle = "px-2 py-1 bg-red-500 text-white text-xs font-bold rounded-md hover:bg-red-600 transition-colors";

const EmpreendimentoFormEspecifico: React.FC<EmpreendimentoFormEspecificoProps> = ({
    basicData,
    onSave,
    onBack,
    initialSpecificData
}) => {
    // --- ESTADO PRINCIPAL ATUALIZADO PARA SUPORTAR TORRES ---
    const [formData, setFormData] = useState({
        fase_empreendimento: FaseEmpreendimentoEnum.PLANTA,
        tipo_empreendimento: [] as number[],
        item_empreendimento: [] as number[],
        torres: [{ nome: 'Torre 1', unidades: [] }] as Torre[],
        unidades_sem_torre: [] as Unidade[],
    });

    // --- NOVO ESTADO PARA A IMAGEM E ETAPAS ---
    const [imagemFile, setImagemFile] = useState<File | null>(null);
    // NOVO ESTADO PARA O ARQUIVO PDF E O NOME DO ARQUIVO
    const [pdfFile, setPdfFile] = useState<File | null>(null);
    const [step, setStep] = useState(1);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [pdfFileName, setPdfFileName] = useState<string | null>(null);

    // Estado para controlar a aba ativa
    const [activeTab, setActiveTab] = useState(0);

    // Estado do modal adaptado para o novo contexto
    const [selectedUnidade, setSelectedUnidade] = useState<{ type: 'torre' | 'avulsa'; torreIndex?: number; andar?: number; unidadeIndex: number } | null>(null);
    const [unidadeTempData, setUnidadeTempData] = useState<Unidade | null>(null);

    // Demais estados (sem alterações)
    const [showTiposModal, setShowTiposModal] = useState(false);
    const [showItensModal, setShowItensModal] = useState(false);
    const [showTiposParteModal, setShowTiposParteModal] = useState(false);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<string[]>([]);
    const itemEmpreendimentoService = new ItemEmpreendimentoService();
    const tipoParteEmpreendimentoService = new TipoParteEmpreendimentoService();

    // Cache para definições de tipo de parte para evitar buscas repetidas
    const [tiposCache, setTiposCache] = useState<Record<number, TipoParteEmpreendimento>>({});

    // --- EFEITOS ---
    useEffect(() => {
        // Carrega dados essenciais na montagem do componente
    }, []);

    useEffect(() => {
        // Lógica para restaurar o estado do formulário ao voltar para esta etapa
        if (initialSpecificData) {
            setFormData(prev => ({
                ...prev,
                fase_empreendimento: initialSpecificData.fase_empreendimento || prev.fase_empreendimento,
                tipo_empreendimento: initialSpecificData.tipo_empreendimento || prev.tipo_empreendimento,
                item_empreendimento: initialSpecificData.item_empreendimento || prev.item_empreendimento,
                torres: initialSpecificData.torres && initialSpecificData.torres.length > 0 ? initialSpecificData.torres : prev.torres,
                unidades_sem_torre: initialSpecificData.unidades_sem_torre || prev.unidades_sem_torre,
            }));
            if (initialSpecificData.imagem_empreendimento) {
                setImagemFile(initialSpecificData.imagem_empreendimento);
                setPreviewUrl(URL.createObjectURL(initialSpecificData.imagem_empreendimento));
            }
            // ATUALIZADO: Restaura o estado do PDF também
            if (initialSpecificData.tabela_preco) {
                setPdfFile(initialSpecificData.tabela_preco);
                setPdfFileName(initialSpecificData.tabela_preco_preco);
            }
        }
    }, [initialSpecificData]);

    // Efeito para buscar a definição do tipo de unidade quando ela muda no modal
    useEffect(() => {
        const loadTipoDefinition = async () => {
            const tiposToFetch: number[] = [];

            // Verifica se existem tipos de parte para buscar
            if (unidadeTempData?.tipo_parte_empreendimento && Array.isArray(unidadeTempData.tipo_parte_empreendimento)) {
                unidadeTempData.tipo_parte_empreendimento.forEach(tipoId => {
                    const numericId = Number(tipoId);
                    // Verifica se o ID é um número válido e se já não está no cache
                    if (!isNaN(numericId) && !tiposCache[numericId]) {
                        tiposToFetch.push(numericId);
                    }
                });
            }

            // Se houver tipos para buscar, faça a requisição para cada um
            if (tiposToFetch.length > 0) {
                const promises = tiposToFetch.map(id => tipoParteEmpreendimentoService.getTipoById(id));
                try {
                    const fetchedTipos = await Promise.all(promises);
                    const newCache = fetchedTipos.reduce((acc, tipo) => {
                        if (tipo && tipo.id) {
                            acc[Number(tipo.id)] = tipo;
                        }
                        return acc;
                    }, {} as Record<number, any>);
                    setTiposCache(prev => ({ ...prev, ...newCache }));
                } catch (error) {
                    console.error("Erro ao carregar definições de tipos de parte:", error);
                }
            }
        };
        loadTipoDefinition();
    }, [unidadeTempData?.tipo_parte_empreendimento]);


    // --- FUNÇÕES DE LÓGICA E VALIDAÇÃO ---
    const validateUnidade = (unidade: Unidade | undefined): { isValid: boolean; isPartial: boolean } => {
        if (!unidade || Object.keys(unidade).length <= 1) return { isValid: false, isPartial: false };

        const requiredFields = ['nome', 'tipo_parte_empreendimento', 'area_privativa_m2', 'valor_imovel'];
        let missing = requiredFields.filter(field => !unidade[field as keyof Unidade] || String(unidade[field as keyof Unidade]).trim() === '');

        // Validar atributos extras obrigatórios para a célula ficar verde

        // CORREÇÃO: Retorna isPartial: true para unidades válidas, para que a lógica de cor funcione
        if (missing.length === 0) return { isValid: true, isPartial: true };
        return { isValid: false, isPartial: true };
    };

    const validateFormData = async (): Promise<{ isValid: boolean; errors: string[] }> => {
        const validationErrors: string[] = [];
        if (!basicData.nome?.trim()) validationErrors.push("Nome do empreendimento é obrigatório");
        let hasUnits = false;

        formData.torres.forEach((torre) => {
            torre.unidades.forEach((andar, andarIndex) => {
                andar.forEach((unidade, unidadeIndex) => {
                    if (unidade && Object.keys(unidade).length > 1) {
                        hasUnits = true;
                        const { isPartial, isValid } = validateUnidade(unidade);
                        if (isPartial && !isValid) {
                            validationErrors.push(`Torre "${torre.nome}", Unidade ${andarIndex + 1}-${unidadeIndex + 1}: Campos incompletos.`);
                        }
                    }
                });
            });
        });
        formData.unidades_sem_torre.forEach((unidade, index) => {
            if (unidade && Object.keys(unidade).length > 1) {
                hasUnits = true;
                const { isPartial, isValid } = validateUnidade(unidade);
                if (isPartial && !isValid) {
                    validationErrors.push(`Unidade Avulsa "${unidade.nome || `item ${index + 1}`}" está com campos incompletos.`);
                }
            }
        });

        if (!hasUnits) validationErrors.push("Pelo menos uma unidade deve ser cadastrada");
        return { isValid: validationErrors.length === 0, errors: validationErrors };
    };

    // --- LÓGICA DE SUBMISSÃO REFEITA PARA ENVIAR TODAS AS TORRES ---
    const formatDataForSubmission = () => {
        const partesDasTorres = formData.torres.flatMap(torre =>
            torre.unidades.flat().filter(u => u && Object.keys(u).length > 1)
        );
        const partesAvulsas = formData.unidades_sem_torre.filter(u => u && Object.keys(u).length > 1);
        const todasAsPartes = [...partesDasTorres, ...partesAvulsas];

        return {
            ...basicData,
            fase_empreendimento: formData.fase_empreendimento,
            tipo_empreendimento: formData.tipo_empreendimento,
            item_empreendimento: formData.item_empreendimento,
            partes_empreendimento: todasAsPartes.map(parte => ({
                ...parte,
                nome: `${basicData.nome} - ${parte.nome}`,
                area_privativa_m2: parseFloat(String(parte.area_privativa_m2).replace(',', '.')) || '0.00',
                valor_imovel: parseFloat(String(parte.valor_imovel).replace(',', '.')) || '0.00',
                tipo_parte_empreendimento: Array.isArray(parte.tipo_parte_empreendimento)
                    ? parte.tipo_parte_empreendimento
                    : (parte.tipo_parte_empreendimento ? [parte.tipo_parte_empreendimento] : []),
            })),
            imagem_empreendimento: imagemFile,
            // NOVO: Adicionando o arquivo PDF ao objeto de submissão
            tabela_preco: pdfFile,
        };
    };

    const handleInputChange = (field: string, value: any) => setFormData(prev => ({ ...prev, [field]: value }));

    const addTorre = () => {
        const novaTorreIndex = formData.torres.length;
        setFormData(prev => ({
            ...prev,
            torres: [...prev.torres, { nome: `Torre ${novaTorreIndex + 1}`, unidades: [] }]
        }));
        setActiveTab(novaTorreIndex);
    };

    const removeTorre = (indexToRemove: number) => {
        if (formData.torres.length <= 1) {
            return;
        }
        setFormData(prev => ({
            ...prev,
            torres: prev.torres.filter((_, index) => index !== indexToRemove)
        }));
        setActiveTab(0);
    };

    const addUnidadeAvulsa = () => setFormData(prev => ({ ...prev, unidades_sem_torre: [...prev.unidades_sem_torre, { torre: 0 }] }));
    const removeUnidadeAvulsa = (index: number) => setFormData(prev => ({ ...prev, unidades_sem_torre: prev.unidades_sem_torre.filter((_, i) => i !== index) }));

    const addAndar = () => {
        if (activeTab >= formData.torres.length) return;
        setFormData(prev => {
            const novasTorres = JSON.parse(JSON.stringify(prev.torres)); // Deep copy para evitar mutação
            novasTorres[activeTab].unidades.push([]);
            return { ...prev, torres: novasTorres };
        });
    };

    const removeAndar = (andarIndex: number) => {
        if (activeTab >= formData.torres.length) return;
        setFormData(prev => {
            const novasTorres = JSON.parse(JSON.stringify(prev.torres));
            novasTorres[activeTab].unidades = novasTorres[activeTab].unidades.filter((_: any, i: number) => i !== andarIndex);
            return { ...prev, torres: novasTorres };
        });
    };

    const addUnidadeToAndar = (andarIndex: number) => {
        if (activeTab >= formData.torres.length) return;
        setFormData(prev => {
            const novasTorres = JSON.parse(JSON.stringify(prev.torres));
            novasTorres[activeTab].unidades[andarIndex].push({ torre: activeTab + 1 });
            return { ...prev, torres: novasTorres };
        });
    };

    const removeUnidade = (andarIndex: number, unidadeIndex: number) => {
        if (activeTab >= formData.torres.length) return;
        setFormData(prev => {
            const novasTorres = JSON.parse(JSON.stringify(prev.torres));
            novasTorres[activeTab].unidades[andarIndex] = novasTorres[activeTab].unidades[andarIndex].filter((_: any, i: number) => i !== unidadeIndex);
            return { ...prev, torres: novasTorres };
        });
    };

    const openUnidadeEditor = (type: 'torre' | 'avulsa', unidadeIndex: number, andar?: number) => {
        let currentData: Partial<Unidade>;
        const defaultUnidade: Unidade = { 
            nome: '', 
            torre: 0, 
            was_lancamento: false, 
            ano_disponibilidade: new Date().getFullYear(), 
            mes_disponibilidade: 1, 
            origem_recurso: 1, 
            area_privativa_m2: '', 
            valor_imovel: '', 
            forma_pagamento: '', 
            tipo_parte_empreendimento: [], 
            atributos_extras: {}, 
            disponibilidade: 1, 
            quantidade_garagem: 0,
            mcmv: false, // VALOR PADRÃO
            tipo_produto: 2, // VALOR PADRÃO (2 = Apartamento)
        };

        if (type === 'torre' && andar !== undefined) {
            currentData = formData.torres[activeTab].unidades[andar][unidadeIndex] || {};
            setSelectedUnidade({ type, torreIndex: activeTab, andar, unidadeIndex });
            setUnidadeTempData({ ...defaultUnidade, ...currentData, torre: activeTab + 1 });
        } else {
            currentData = formData.unidades_sem_torre[unidadeIndex] || {};
            setSelectedUnidade({ type, unidadeIndex });
            setUnidadeTempData({ ...defaultUnidade, ...currentData, torre: 0 });
        }
    };

    const saveUnidade = () => {
        if (!selectedUnidade || !unidadeTempData) return;
        setFormData(prev => {
            const newFormData = { ...prev };
            if (selectedUnidade.type === 'torre' && selectedUnidade.torreIndex !== undefined && selectedUnidade.andar !== undefined) {
                const { torreIndex, andar, unidadeIndex } = selectedUnidade;
                newFormData.torres[torreIndex].unidades[andar][unidadeIndex] = { ...unidadeTempData };
            } else {
                newFormData.unidades_sem_torre[selectedUnidade.unidadeIndex] = { ...unidadeTempData };
            }
            return newFormData;
        });
        setSelectedUnidade(null);
        setUnidadeTempData(null);
    };

    const handleUnidadeTempChange = (field: string, value: any) => {
        setUnidadeTempData(prev => {
            if (!prev) return null;
            const updated = { ...prev, [field]: value };
            if (field === 'tipo_parte_empreendimento') {
                updated.atributos_extras = {};
            }
            return updated;
        });
    };

    const handleAtributoExtraTempChange = (key: string, value: any) => {
        setUnidadeTempData(prev => prev ? { ...prev, atributos_extras: { ...prev.atributos_extras, [key]: value } } : null);
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        setImagemFile(file);
        if (file) {
            setPreviewUrl(URL.createObjectURL(file));
        } else {
            setPreviewUrl(null);
        }
    };

    // NOVA FUNÇÃO: Manipula o upload do arquivo PDF
    const handlePdfChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        setPdfFile(file);
        if (file) {
            setPdfFileName(file.name);
        } else {
            setPdfFileName(null);
        }
    };

    const handleNextStep = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setErrors([]);
        const { isValid, errors: validationErrors } = await validateFormData();
        if (!isValid) {
            setErrors(validationErrors);
            setLoading(false);
            return;
        }
        setStep(2);
        setLoading(false);
    };

    const handleFinalSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        const formattedData = formatDataForSubmission();
        onSave(formattedData);
        setLoading(false);
    };

    const handleBackStep = () => {
        if (step === 2) {
            setStep(1);
        } else {
            onBack?.(formData);
        }
    };

    const [tipos, setTipos] = useState<TipoEmpreendimento[]>([]);
    const [tiposParte, setTiposParte] = useState<TipoParteEmpreendimento[]>([]);


    const getRowCount = (andarIndex: number) => formData.torres[activeTab]?.unidades[andarIndex]?.filter(u => validateUnidade(u).isValid).length || 0;
    const getColCount = (colIndex: number) => formData.torres[activeTab]?.unidades.reduce((count, row) => count + (validateUnidade(row[colIndex]).isValid ? 1 : 0), 0) || 0;

    // No arquivo EmpreendimentoFormEspecifico.tsx
    const renderAtributosExtrasTemp = () => {
        // Mesma lógica de consolidação de atributos. Está correta.
        if (!unidadeTempData?.tipo_parte_empreendimento || unidadeTempData.tipo_parte_empreendimento.length === 0) {
            return null;
        }

        const consolidatedAtributos: Record<string, any> = {};
        unidadeTempData.tipo_parte_empreendimento.forEach(tipoId => {
            const tipo = tiposCache[Number(tipoId)];
            if (tipo && tipo.atributos_definidos) {
                Object.assign(consolidatedAtributos, tipo.atributos_definidos);
            }
        });

        return (
            <div className="mt-6 pt-4 border-t border-gray-200">
                <h5 className="text-md font-semibold text-gray-800 mb-4">
                    Atributos Específicos
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                    {Object.entries(consolidatedAtributos).map(([key, attr]) => (
                        <div key={key}>
                            <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                                {attr.label || key}
                                {attr.required && <span className="ml-1 text-red-500">*</span>}
                            </label>

                            {/* Renderiza o input com base no tipo de atributo */}
                            {attr.type === 'text' && (
                                <input
                                    type="text"
                                    value={unidadeTempData?.atributos_extras?.[key] || ''}
                                    onChange={(e) => handleAtributoExtraTempChange(key, e.target.value)}
                                    className={inputStyle}
                                    placeholder={attr.placeholder || `Digite ${attr.label || key}`}
                                />
                            )}

                            {attr.type === 'number' && (
                                <input
                                    type="number"
                                    value={unidadeTempData?.atributos_extras?.[key] || ''}
                                    onChange={(e) => handleAtributoExtraTempChange(key, e.target.value)}
                                    className={inputStyle}
                                    placeholder={attr.placeholder || `Digite ${attr.label || key}`}
                                    min={attr.min}
                                    max={attr.max}
                                    step={attr.step}
                                />
                            )}

                            {attr.type === 'textarea' && (
                                <textarea
                                    value={unidadeTempData?.atributos_extras?.[key] || ''}
                                    onChange={(e) => handleAtributoExtraTempChange(key, e.target.value)}
                                    className={inputStyle}
                                    placeholder={attr.placeholder || `Digite ${attr.label || key}`}
                                    rows={3}
                                />
                            )}

                            {attr.type === 'select' && (
                                <select
                                    value={unidadeTempData?.atributos_extras?.[key] || ''}
                                    onChange={(e) => handleAtributoExtraTempChange(key, e.target.value)}
                                    className={inputStyle}
                                >
                                    <option value="" disabled>Selecione uma opção</option>
                                    {(attr.options || []).map((option: string) => (
                                        <option key={option} value={option}>{option}</option>
                                    ))}
                                </select>
                            )}

                            {attr.type === 'checkbox' && (
                                <div className="mt-2">
                                    <label className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={unidadeTempData?.atributos_extras?.[key] || false}
                                            onChange={(e) => handleAtributoExtraTempChange(key, e.target.checked)}
                                            className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
                                        />
                                        <span className="ml-2 text-sm text-gray-700">
                                            {attr.label || key}
                                        </span>
                                    </label>
                                </div>
                            )}

                            {attr.type === 'multiselect' && (
                                <select
                                    multiple
                                    value={unidadeTempData?.atributos_extras?.[key] || []}
                                    onChange={(e) => {
                                        const selectedValues = Array.from(e.target.selectedOptions).map(option => option.value);
                                        handleAtributoExtraTempChange(key, selectedValues);
                                    }}
                                    className={`${inputStyle} h-24`} // Altura ajustada para multiselect
                                >
                                    {(attr.options || []).map((option: string) => (
                                        <option key={option} value={option}>{option}</option>
                                    ))}
                                </select>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    return (
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <form onSubmit={step === 1 ? handleNextStep : handleFinalSubmit} className="space-y-8">
                {Array.isArray(errors) && errors.length > 0 && (
                    <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-md">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800">
                                    Foram encontrados {errors.length} erros:
                                </h3>
                                <div className="mt-2 text-sm text-red-700">
                                    <ul className="list-disc pl-5 space-y-1">
                                        {errors.map((error: string, index: number) => (
                                            <li key={index}>{error}</li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {step === 1 && (
                    <>
                        <div className="bg-white shadow-md rounded-lg ">
                            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Fase e Tipo do Empreendimento</h3>
                            </div>
                            <div className="p-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">Fase do Empreendimento *</label>
                                        <select value={formData.fase_empreendimento} onChange={(e) => handleInputChange('fase_empreendimento', parseInt(e.target.value))} className={inputStyle} required>
                                            <option value={FaseEmpreendimentoEnum.PLANTA}>Planta</option>
                                            <option value={FaseEmpreendimentoEnum.OBRAS}>Obras</option>
                                            <option value={FaseEmpreendimentoEnum.PRONTO}>Pronto</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1 ">Tipo do Empreendimento *</label>
                                        <div className="flex space-x-2 items-center ">
                                            <Select
                                                isMulti
                                                options={tiposDeEmpreendimento} // Array de objetos { value: number, label: string }
                                                value={tiposDeEmpreendimento.filter(option =>
                                                    formData.tipo_empreendimento.includes(option.value)
                                                )}
                                                onChange={(selectedOptions) => {
                                                    // Mapeia as opções selecionadas para um array de IDs numéricos
                                                    const selectedIds = selectedOptions ? selectedOptions.map(option => option.value) : [];
                                                    setFormData(prev => ({
                                                        ...prev,
                                                        tipo_empreendimento: selectedIds, // Salva o array de IDs no estado
                                                    }));
                                                }}
                                                placeholder="Selecione os tipos"
                                                className="react-select-container flex-1"
                                                classNamePrefix="react-select"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <ItensEmpreendimentoSelector
                            // Passe apenas as props que ele ainda precisa
                            selectedItens={formData.item_empreendimento}
                            onSelectionChange={(newSelectedItens) => {
                                handleInputChange('item_empreendimento', newSelectedItens);
                            }}
                            onManageItensClick={() => setShowItensModal(true)}
                            successButtonStyle={successButtonStyle}
                        />

                        {/* --- SEÇÃO DE CONFIGURAÇÃO DE UNIDADES SUBSTITUÍDA --- */}
                        <div className="bg-white shadow-md rounded-lg overflow-hidden">
                            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Configuração das Unidades</h3>
                            </div>
                            <div className="p-6">
                                <div className="flex space-x-2 mb-4">
                                    <button type="button" onClick={addTorre} className="flex items-center gap-2 px-3 py-2 text-sm bg-blue-600 text-white font-semibold rounded-md shadow-sm hover:bg-blue-700">
                                        <Plus size={16} /> Adicionar Torre
                                    </button>
                                    <button type="button" onClick={addUnidadeAvulsa} className="flex items-center gap-2 px-3 py-2 text-sm bg-teal-600 text-white font-semibold rounded-md shadow-sm hover:bg-teal-700">
                                        <Plus size={16} /> Adicionar Unidade Avulsa
                                    </button>
                                </div>
                                <div className="border-b border-gray-200 mb-6">
                                    <nav className="-mb-px flex space-x-6 overflow-x-auto" aria-label="Tabs">
                                        {formData.torres.map((torre, index) => (
                                            <div key={index} className={`group relative flex-shrink-0 whitespace-nowrap inline-flex items-center py-4 px-1 border-b-2 ${activeTab === index ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                                                <button type="button" onClick={() => setActiveTab(index)} className="flex items-center">
                                                    <Building size={16} className="mr-2" />
                                                    <span>{torre.nome}</span>
                                                </button>
                                                {formData.torres.length > 1 && (
                                                    <button type="button" onClick={() => removeTorre(index)} className="ml-2 text-gray-400 hover:text-red-500 opacity-50 group-hover:opacity-100 transition-opacity">
                                                        <X size={14} />
                                                    </button>
                                                )}
                                            </div>
                                        ))}
                                        <button type="button" onClick={() => setActiveTab(formData.torres.length)} className={`flex-shrink-0 whitespace-nowrap inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${activeTab === formData.torres.length ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                                            <Home size={16} className="mr-2" />
                                            Outras Unidades ({formData.unidades_sem_torre.length})
                                        </button>
                                    </nav>
                                </div>

                                {activeTab < formData.torres.length ? (
                                    <>
                                        {formData.torres[activeTab]?.unidades.length > 0 ? (
                                            <div className="overflow-x-auto border rounded-lg">
                                                <table className="min-w-full divide-y divide-gray-200">
                                                    <thead className="bg-gray-100">
                                                        <tr>
                                                            <th className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Andar</th>
                                                            {Array.from({ length: Math.max(0, ...(formData.torres[activeTab]?.unidades.map(row => row.length) || [1])) }).map((_, col) => (
                                                                <th key={col} className="px-4 py-3 text-center text-xs font-bold text-gray-600 uppercase tracking-wider">Unid. {col + 1} ({getColCount(col)})</th>
                                                            ))}
                                                            <th className="px-4 py-3 text-right text-xs font-bold text-gray-600 uppercase tracking-wider">Ações</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="bg-white divide-y divide-gray-200">
                                                        {formData.torres[activeTab].unidades.map((row, rowIndex) => (
                                                            <tr key={rowIndex} className="hover:bg-gray-50">
                                                                <td className="px-4 py-3 whitespace-nowrap text-sm font-semibold text-gray-800">{rowIndex + 1}º Andar ({getRowCount(rowIndex)})</td>
                                                                {Array.from({ length: Math.max(0, ...(formData.torres[activeTab].unidades.map(r => r.length))) || 0 }).map((_, colIndex) => {
                                                                    if (colIndex < row.length) {
                                                                        const { isValid, isPartial } = validateUnidade(row[colIndex]);
                                                                        let cellStyle = !isPartial ? 'bg-gray-100 text-gray-500' : (isValid ? 'bg-emerald-200 text-emerald-900' : 'bg-red-100 text-red-800');
                                                                        return (
                                                                            <td key={colIndex} className="p-2 text-center align-middle">
                                                                                <div onClick={() => openUnidadeEditor('torre', colIndex, rowIndex)} className={`group relative p-3 h-16 flex items-center justify-center rounded-md cursor-pointer transition-all hover:shadow-lg hover:scale-105 transform ${cellStyle}`}>
                                                                                    <Building className="h-8 w-8" />
                                                                                    <button type="button" className="absolute top-1 right-1 h-5 w-5 bg-red-100 text-red-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100" onClick={e => { e.stopPropagation(); removeUnidade(rowIndex, colIndex); }}>&times;</button>
                                                                                </div>
                                                                            </td>
                                                                        );
                                                                    }
                                                                    return <td key={colIndex} className="p-2"><div className="h-16 bg-gray-50 rounded-md"></div></td>;
                                                                })}
                                                                <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                                                    <div className="flex justify-end items-center space-x-2">
                                                                        <button type="button" className="px-2 py-1 bg-green-500 text-white text-xs font-bold rounded-md" onClick={() => addUnidadeToAndar(rowIndex)}>+ Unidade</button>
                                                                        <button type="button" className={dangerButtonStyle} onClick={() => removeAndar(rowIndex)}>Remover Andar</button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                        ) : (
                                            <p className="text-sm text-gray-500 text-center py-4">Nenhum andar adicionado a esta torre. Clique em "Adicionar Novo Andar" para começar.</p>
                                        )}
                                        <div className="mt-6">
                                            <button type="button" onClick={addAndar} className={successButtonStyle}>Adicionar Novo Andar</button>
                                        </div>
                                    </>
                                ) : (
                                    <div className="space-y-3">
                                        {formData.unidades_sem_torre.map((unidade, index) => {
                                            const { isValid, isPartial } = validateUnidade(unidade);
                                            let statusColor = !isPartial ? 'border-gray-300' : (isValid ? 'border-emerald-500' : 'border-red-500');
                                            return (
                                                <div key={index} onClick={() => openUnidadeEditor('avulsa', index)} className={`flex items-center justify-between p-3 border-l-4 ${statusColor} bg-white shadow-sm rounded-md cursor-pointer hover:shadow-md transition-shadow`}>
                                                    <div className="flex items-center gap-3">
                                                        <Home className="text-gray-600" />
                                                        <span className="font-medium text-gray-800">{unidade.nome || `Nova Unidade Avulsa ${index + 1}`}</span>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <button type="button" onClick={(e) => { e.stopPropagation(); removeUnidadeAvulsa(index); }} className={dangerButtonStyle}>Remover</button>
                                                    </div>
                                                </div>
                                            )
                                        })}
                                        {formData.unidades_sem_torre.length === 0 && <p className="text-sm text-gray-500">Nenhuma unidade avulsa adicionada.</p>}
                                    </div>
                                )}
                            </div>
                        </div>
                    </>
                )}

                {step === 2 && (
                    <div>
                        <div className="bg-white shadow-md rounded-lg overflow-hidden w-full">
                            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Upload da Imagem do Empreendimento</h3>
                                <p className="mt-1 text-sm text-gray-600">Selecione uma imagem principal para o seu empreendimento.</p>
                            </div>
                            <div className="p-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Imagem de Destaque</label>
                                <input
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageChange}
                                    className={inputStyle}
                                />
                                {previewUrl && (
                                    <div className="mt-4 border border-gray-300 rounded-md overflow-hidden">
                                        <img src={previewUrl} alt="Preview" className="w-full h-auto object-cover" />
                                    </div>
                                )}
                                {!imagemFile && (
                                    <p className="mt-2 text-sm text-gray-500">Nenhuma imagem selecionada.</p>
                                )}
                            </div>
                        </div>

                        <div className="bg-white shadow-md rounded-lg overflow-hidden w-full mt-6">
                            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Upload da tabela de preço do Empreendimento</h3>
                                <p className="mt-1 text-sm text-gray-600">Selecione uma PDF para o empreendimento.</p>
                            </div>
                            <div className="p-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">tabela de preço</label>
                                <input
                                    type="file"
                                    accept=".pdf"
                                    // ATUALIZADO: Usando a nova função para lidar com o PDF
                                    onChange={handlePdfChange}
                                    className={inputStyle}
                                />

                                {pdfFileName ? (
                                    <p className="mt-2 text-sm text-gray-700">Arquivo selecionado: <span className="font-semibold">{pdfFileName}</span></p>
                                ) : (
                                    <p className="mt-2 text-sm text-gray-500">Nenhum arquivo PDF selecionado.</p>
                                )}
                            </div>
                        </div>
                    </div>
                )}


                <div className="flex justify-between items-center pt-6 border-t">
                    <div>
                        {onBack && step === 1 && (
                            <button type="button" onClick={() => onBack(formData)} className={secondaryButtonStyle}>Voltar</button>
                        )}
                        {step === 2 && (
                            <button type="button" onClick={handleBackStep} className={secondaryButtonStyle}>Voltar</button>
                        )}
                    </div>
                    <div className="flex space-x-4">
                        {step === 1 && (
                            <button type="submit" disabled={loading} className={primaryButtonStyle}>
                                Próxima Etapa
                            </button>
                        )}
                        {step === 2 && (
                            <button type="submit" disabled={loading} className={primaryButtonStyle}>
                                {loading ? 'Finalizando...' : 'Finalizar Cadastro'}
                            </button>
                        )}
                    </div>
                </div>
            </form>

            {selectedUnidade && unidadeTempData && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg shadow-2xl max-w-3xl w-full flex flex-col max-h-[90vh]">
                        <div className="px-6 py-4 border-b">
                            <h3 className="text-xl font-semibold text-gray-900">
                                {selectedUnidade.type === 'torre'
                                    ? `Editar Unidade ${selectedUnidade.andar! + 1}-${selectedUnidade.unidadeIndex + 1} (${formData.torres[selectedUnidade.torreIndex!].nome})`
                                    : `Editar Unidade Avulsa`
                                }
                            </h3>
                        </div>
                        <div className="p-6 overflow-y-auto">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Nome da Unidade *</label>
                                    <input type="text" value={unidadeTempData.nome || ''} onChange={(e) => handleUnidadeTempChange('nome', e.target.value)} className={inputStyle} placeholder="Ex: Apartamento 2 quartos com suíte" />
                                </div>
                                <div>
                                    <TipoParteSelector
                                        label="Características da unidade"
                                        value={unidadeTempData.tipo_parte_empreendimento || []}
                                        onChange={(value) => handleUnidadeTempChange('tipo_parte_empreendimento', value)}

                                    />
                                </div>
                                {/* Disponibilidade */}
                                <div className="block text-sm font-medium text-gray-700 mb-1">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Disponibilidade</label>
                                    <select
                                        value={unidadeTempData.disponibilidade || ''}
                                        onChange={(e) => handleUnidadeTempChange('disponibilidade', e.target.value)}
                                        className={inputStyle}
                                    >
                                        <option value="" disabled>Selecione uma opção</option>
                                        <option value="1">Disponível</option>
                                        <option value="2">Vendido</option>
                                        <option value="3">Distratado</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Área Privativa (m²) *</label>
                                    <input type="text" value={unidadeTempData.area_privativa_m2 || ''} onChange={(e) => handleUnidadeTempChange('area_privativa_m2', e.target.value)} className={inputStyle} placeholder="Ex: 75.50" />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Valor do Imóvel *</label>
                                    <input type="text" value={unidadeTempData.valor_imovel || ''} onChange={(e) => handleUnidadeTempChange('valor_imovel', e.target.value)} className={inputStyle} placeholder="Ex: 450000.00" />
                                </div>
                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Forma de Pagamento</label>
                                    <input type="text" value={unidadeTempData.forma_pagamento || ''} onChange={(e) => handleUnidadeTempChange('forma_pagamento', e.target.value)} className={inputStyle} placeholder="Ex: Financiamento, À vista" />
                                </div>

                                {/* ========= INÍCIO DOS NOVOS CAMPOS ========= */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Tipo da Unidade</label>
                                    <select
                                        value={unidadeTempData.tipo_produto || 2}
                                        onChange={(e) => handleUnidadeTempChange('tipo_produto', parseInt(e.target.value))}
                                        className={inputStyle}
                                    >
                                        <option value={1}>Lote</option>
                                        <option value={2}>Apartamento</option>
                                        <option value={3}>Comercial</option>
                                        <option value={4}>Casa</option>
                                        <option value={5}>Compacto</option>
                                    </select>
                                </div>

                                {/* Quantidade de Garagem */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Quantidade de Garagem</label>
                                    <input
                                        type="number"
                                        value={unidadeTempData.quantidade_garagem || 0}
                                        onChange={(e) => handleUnidadeTempChange('quantidade_garagem', e.target.value)}
                                        className={inputStyle}
                                        placeholder="Ex: 1"
                                    />
                                </div>
                                {/* ========= FIM DOS CAMPOS INTERMEDIÁRIOS ========= */}
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Ano de Disponibilidade</label>
                                    <select value={unidadeTempData.ano_disponibilidade || 2024} onChange={(e) => handleUnidadeTempChange('ano_disponibilidade', parseInt(e.target.value))} className={inputStyle}>
                                        {[...Array(10)].map((_, i) => <option key={2024 + i} value={2024 + i}>{2024 + i}</option>)}
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Mês de Disponibilidade</label>
                                    <select value={unidadeTempData.mes_disponibilidade || 1} onChange={(e) => handleUnidadeTempChange('mes_disponibilidade', parseInt(e.target.value))} className={inputStyle}>
                                        {['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'].map((mes, i) => (
                                            <option key={i + 1} value={i + 1}>{mes}</option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Origem do Recurso</label>
                                    <select value={unidadeTempData.origem_recurso || 1} onChange={(e) => handleUnidadeTempChange('origem_recurso', parseInt(e.target.value))} className={inputStyle}>
                                        <option value={1}>Próprio</option>
                                        <option value={2}>SFH</option>
                                        <option value={3}>Condomínio</option>
                                        <option value={4}>Cooperativa</option>
                                        <option value={5}>Próprio + SFH</option>
                                        <option value={6}>Outros</option>
                                        <option value={7}>MCMV</option>
                                    </select>
                                </div>
                                <div className="flex items-center pt-2">
                                    <label className="flex items-center cursor-pointer">
                                        <input type="checkbox" checked={unidadeTempData.was_lancamento || false} onChange={(e) => handleUnidadeTempChange('was_lancamento', e.target.checked)} className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
                                        <span className="ml-2 text-sm text-gray-800 font-medium">Marcar como Lançamento</span>
                                    </label>
                                </div>
                                {/* ========= INÍCIO DO NOVO CHECKBOX ========= */}
                                <div className="flex items-center pt-2">
                                    <label className="flex items-center cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={unidadeTempData.mcmv || false}
                                            onChange={(e) => handleUnidadeTempChange('mcmv', e.target.checked)}
                                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                        />
                                        <span className="ml-2 text-sm text-gray-800 font-medium">Minha Casa Minha Vida</span>
                                    </label>
                                </div>
                                {/* ========= FIM DO NOVO CHECKBOX ========= */}

                            </div>
                            {renderAtributosExtrasTemp()}
                        </div>
                        <div className="px-6 py-4 bg-gray-50 border-t flex justify-end space-x-4">
                            <button type="button" onClick={() => setSelectedUnidade(null)} className={secondaryButtonStyle}>Cancelar</button>
                            <button type="button" onClick={saveUnidade} className={primaryButtonStyle}>Salvar Unidade</button>
                        </div>
                    </div>
                </div>
            )}

            {showItensModal && <GerenciarItensEmpreendimento onClose={() => setShowItensModal(false)} />}
            {showTiposParteModal && <GerenciarTiposParteEmpreendimento onClose={() => setShowTiposParteModal(false)} onTipoSelected={() => { }} />}
        </div>
    );
};

export default EmpreendimentoFormEspecifico;