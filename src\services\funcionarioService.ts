import api from "./api";
import {
  <PERSON><PERSON><PERSON>,
  FuncionarioCreate,
  FuncionarioUpdate,
  PaginatedFuncionarioList,
} from "../types/Funcionario";
import { PaginatedVinculacaoMembros } from "../types/franquia";

const BASE_URL = "/vinculacao/";

export const vinculacao = {
  async listar(
    limit: number,
    offset: number,
    codigovinculacao: string,
    status?: string,
    search?: string,
    tipo?: string
  ): Promise<PaginatedVinculacaoMembros> {
    const params = new URLSearchParams();
    params.append("limit", limit.toString());
    params.append("offset", offset.toString());
    params.append("codigovinculacao", codigovinculacao);

    if (status) {
      params.append("status", status);
    }
    if (search) params.append("search", search);
    if (tipo) params.append("tipo", tipo);
    const response = await api.get(`${BASE_URL}?${params.toString()}`);
    return response.data;
  },
  async aceitar(id: number) {
    return api.post(`/vinculacao/${id}/aceitar/`);
  },
  async rejeitar(id: number) {
    return api.post(`/vinculacao/${id}/rejeitar/`);
  },
  async meucodigo(): Promise<string> {
    const response = await api.get(`${BASE_URL}meu-codigo/`);
    return response.data.codigovinculacao;
  },

  async criar(data: FuncionarioCreate): Promise<Funcionario> {
    const response = await api.post("create user private/", data);
    return response.data;
  },

  async atualizar(id: number, data: FuncionarioUpdate): Promise<Funcionario> {
    const response = await api.put(`${BASE_URL}${id}/`, data);
    return response.data;
  },

  async atualizarParcial(
    id: number,
    data: Partial<FuncionarioUpdate>
  ): Promise<Funcionario> {
    const response = await api.patch(`${BASE_URL}${id}/`, data);
    return response.data;
  },

  async excluir(id: number): Promise<void> {
    await api.delete(`${BASE_URL}${id}/`);
  },
};
