import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { AreaCidadeForm } from './AreaCidadeForm';
import { areaCidadeService } from '../../services/areaCidadeService';
import { AreaCidadeInput } from '../../types/areaCidade';

export const EditarAreaCidade: React.FC = () => {
  const { id } = useParams();
  const [areaCidade, setAreaCidade] = useState<AreaCidadeInput | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAreaCidade = async () => {
      if (!id) return;

      try {
        const data = await areaCidadeService.buscarPorId(Number(id));
        setAreaCidade({
          nome: data.nome,
          cidade: data.cidade,
          franqueado_id: data.franqueado_id
        });
      } catch (err) {
        console.error('Erro ao carregar área-cidade:', err);
        setError('Erro ao carregar dados da área-cidade');
      } finally {
        setLoading(false);
      }
    };

    fetchAreaCidade();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#32bef0]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">{error}</h3>
          </div>
        </div>
      </div>
    );
  }

  if (!areaCidade) {
    return (
      <div className="rounded-md bg-yellow-50 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Área-cidade não encontrada</h3>
          </div>
        </div>
      </div>
    );
  }

  return <AreaCidadeForm initialData={areaCidade} isEditing={true} />;
}; 