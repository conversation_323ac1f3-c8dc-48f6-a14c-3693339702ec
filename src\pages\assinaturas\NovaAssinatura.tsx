import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AssinaturaForm } from './AssinaturaForm';
import { assinaturaService } from '../../services/assinaturaService';
import { AssinaturaCreate } from '../../types/assinatura';

export const NovaAssinatura: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (data: AssinaturaCreate | any) => {
    setLoading(true);
    try {
      await assinaturaService.criar(data);
      navigate('/assinaturas');
    } catch (error) {
      console.error('Erro ao criar assinatura:', error);
      alert('Erro ao criar assinatura. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-[#29306a]">
            Nova Assinatura
          </h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Preencha os dados para criar uma nova assinatura
          </p>
        </div>

        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <AssinaturaForm
              onSubmit={handleSubmit}
              loading={loading}
              isEditing={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
}; 