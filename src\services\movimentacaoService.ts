import api from './api';
import { Movimentacao, MovimentacaoCreate } from '../types/movimentacao';

export const movimentacaoService = {
  list: async (params?: { limit?: number; offset?: number }) => {
    const response = await api.get('/movimentacoes/', { params });
    return response.data;
  },

  getById: async (id: number) => {
    const response = await api.get(`/movimentacoes/${id}/`);
    return response.data;
  },

  create: async (data: MovimentacaoCreate) => {
    const response = await api.post('/movimentacoes/', data);
    return response.data;
  },

  update: async (id: number, data: Partial<MovimentacaoCreate>) => {
    const response = await api.put(`/movimentacoes/${id}/`, data);
    return response.data;
  },

  delete: async (id: number) => {
    await api.delete(`/movimentacoes/${id}/`);
  }
}; 