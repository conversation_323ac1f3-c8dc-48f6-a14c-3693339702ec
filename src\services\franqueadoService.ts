import { CrudService } from './crud';
import { Franqueado, FranqueadoInput, FranqueadoUpdateInput, PaginatedFranqueadoResponse } from '../types/franqueado';
import api from './api';

class FranqueadoService extends CrudService<Franqueado> {
  constructor() {
    super('/franqueados/');
  }

  async criar(franqueado: FranqueadoInput): Promise<Franqueado> {
    const response = await api.post<Franqueado>('/franqueados/', franqueado);
    return response.data;
  }

  async buscarPorId(id: number): Promise<Franqueado> {
    return this.get(id);
  }

  async atualizar(id: number, franqueado: FranqueadoUpdateInput): Promise<Franqueado> {
    const response = await api.put<Franqueado>(`/franqueados/${id}/`, franqueado);
    return response.data;
  }

  async atualizarParcial(id: number, franqueado: FranqueadoUpdateInput): Promise<Franqueado> {
    const response = await api.patch<Franqueado>(`/franqueados/${id}/`, franqueado);
    return response.data;
  }

  async excluir(id: number): Promise<void> {
    return this.delete(id);
  }

  async listar(page: number = 1, limit: number = 10): Promise<PaginatedFranqueadoResponse> {
    const offset = (page - 1) * limit;
    const response = await api.get<PaginatedFranqueadoResponse>(`/franqueados/?offset=${offset}&limit=${limit}`);
    return response.data;
  }
}

export const franqueadoService = new FranqueadoService(); 