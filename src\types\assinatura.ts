export type RecorrenciaEnum = 'mensal' | 'semestral' | 'anual';

export type FormaPagamentoEnum = 'cartao' | 'boleto' | 'pix' | 'debito';

export interface Assinatura {
  id: number;
  created: string;
  modified: string;
  data_assinatura: string;
  recorrencia: RecorrenciaEnum;
  data_expiracao?: string;
  forma_pagamento: FormaPagamentoEnum;
  assinante: number;
  cidade: number;
  plano: number;
}

export interface AssinaturaCreate {
  recorrencia: RecorrenciaEnum;
  data_expiracao?: string;
  forma_pagamento: FormaPagamentoEnum;
  assinante: number;
  cidade: number;
  plano: number;
}

export interface AssinaturaUpdate {
  recorrencia?: RecorrenciaEnum;
  data_expiracao?: string;
  forma_pagamento?: FormaPagamentoEnum;
  assinante?: number;
  cidade?: number;
  plano?: number;
}

export interface PaginatedAssinaturaList {
  count: number;
  next?: string;
  previous?: string;
  results: Assinatura[];
} 