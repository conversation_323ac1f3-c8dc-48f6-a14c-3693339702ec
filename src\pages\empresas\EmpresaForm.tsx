import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { empresaService } from '../../services/empresaService';
import { EmpresaInput, EmpresaUpdateInput } from '../../types/empresa';

interface EmpresaFormProps {
  empresa?: EmpresaInput;
  isEditing?: boolean;
  empresaId?: number;
}

export const EmpresaForm: React.FC<EmpresaFormProps> = ({ 
  empresa, 
  isEditing = false, 
  empresaId 
}) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<EmpresaInput>({
    razao_social: empresa?.razao_social || '',
    nome_fantasia: empresa?.nome_fantasia || '',
    cnpj: empresa?.cnpj || '',
    endereco: empresa?.endereco || 0,
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (empresa) {
      setFormData({
        razao_social: empresa.razao_social,
        nome_fantasia: empresa.nome_fantasia,
        cnpj: empresa.cnpj,
        endereco: empresa.endereco,
      });
    }
  }, [empresa]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const formatarCNPJ = (value: string) => {
    // Remove tudo que não é dígito
    const cnpj = value.replace(/\D/g, '');
    
    // Aplica a máscara
    return cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5');
  };

  const handleCNPJChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const formattedValue = formatarCNPJ(value);
    setFormData(prev => ({
      ...prev,
      cnpj: formattedValue,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (isEditing && empresaId) {
        await empresaService.atualizar(empresaId, formData);
      } else {
        await empresaService.criar(formData);
      }
      navigate('/empresas');
    } catch (err: any) {
      console.error('Erro ao salvar empresa:', err);
      setError(err.response?.data?.message || 'Erro ao salvar empresa');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="md:grid md:grid-cols-3 md:gap-6">
        <div className="mt-5 md:mt-0 md:col-span-3">
          <form onSubmit={handleSubmit}>
            <div className="shadow sm:rounded-md sm:overflow-hidden">
              <div className="px-4 py-5 bg-white space-y-6 sm:p-6">
                {error && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">{error}</h3>
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <label htmlFor="razao_social" className="block text-sm font-medium text-[#29306a]">
                    Razão Social *
                  </label>
                  <input
                    type="text"
                    name="razao_social"
                    id="razao_social"
                    required
                    maxLength={254}
                    value={formData.razao_social}
                    onChange={handleInputChange}
                    className="mt-1 focus:ring-[#32bef0] focus:border-[#32bef0] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  />
                </div>

                <div>
                  <label htmlFor="nome_fantasia" className="block text-sm font-medium text-[#29306a]">
                    Nome Fantasia *
                  </label>
                  <input
                    type="text"
                    name="nome_fantasia"
                    id="nome_fantasia"
                    required
                    maxLength={254}
                    value={formData.nome_fantasia}
                    onChange={handleInputChange}
                    className="mt-1 focus:ring-[#32bef0] focus:border-[#32bef0] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  />
                </div>

                <div>
                  <label htmlFor="cnpj" className="block text-sm font-medium text-[#29306a]">
                    CNPJ *
                  </label>
                  <input
                    type="text"
                    name="cnpj"
                    id="cnpj"
                    required
                    maxLength={18}
                    placeholder="00.000.000/0000-00"
                    value={formData.cnpj}
                    onChange={handleCNPJChange}
                    className="mt-1 focus:ring-[#32bef0] focus:border-[#32bef0] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  />
                </div>

                <div>
                  <label htmlFor="endereco" className="block text-sm font-medium text-[#29306a]">
                    ID do Endereço *
                  </label>
                  <input
                    type="number"
                    name="endereco"
                    id="endereco"
                    required
                    min="1"
                    value={formData.endereco}
                    onChange={handleInputChange}
                    className="mt-1 focus:ring-[#32bef0] focus:border-[#32bef0] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  />
                  <p className="mt-1 text-sm text-[#64748b]">
                    ID do endereço cadastrado no sistema
                  </p>
                </div>
              </div>
              <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                <button
                  type="button"
                  onClick={() => navigate('/empresas')}
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-[#29306a] hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0] mr-3"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#32bef0] hover:bg-[#2ba8d8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0] disabled:opacity-50"
                >
                  {loading ? 'Salvando...' : (isEditing ? 'Atualizar' : 'Salvar')}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}; 