import api from './api';
import { Endereco, Logradouro } from '../types/empresa';

export interface EnderecoCreate {
  logradouro: number | LogradouroCreate;
  numero?: number;
  complemento?: string;
  ponto_referencia?: string;
  latitude: string;
  longitude: string;
}

export interface LogradouroCreate {
  cidade: number;
  zipcode: string;
  nome: string;
  bairro: string;
}

export class EnderecoService {
  private baseUrl = '/enderecos/';
  private logradouroUrl = '/logradouros/';

  async searchLogradouroByZipcode(zipcode: string): Promise<any> {
    try {
      const response = await api.get(`${this.logradouroUrl}by_zipcode/`, { 
        params: { zipcode } 
      });
      return response.data;
    } catch (error: any) {
      return { results: [] };
    }
  }

  async createLogradouroIfNotExists(data: LogradouroCreate): Promise<Logradouro> {
    try {
      try {
        const existing = await this.searchLogradouroByZipcode(data.zipcode);
        if (existing.results && existing.results.length > 0) {
          const found = existing.results.find((log: any) => 
            log.nome.toLowerCase() === data.nome.toLowerCase() && 
            log.bairro.toLowerCase() === data.bairro.toLowerCase()
          );
          if (found) {
            return found;
          }
        }
      } catch (searchError) {
      }

      const response = await api.post(this.logradouroUrl, data);
      return response.data;
    } catch (error: any) {
      console.error('Erro ao criar logradouro:', error);
      throw error;
    }
  }

  async createEndereco(data: EnderecoCreate): Promise<Endereco> {
    try {
      let logradouroId: number;

      if (typeof data.logradouro === 'number') {
        logradouroId = data.logradouro;
      } else {
        const logradouro = await this.createLogradouroIfNotExists(data.logradouro);
        logradouroId = logradouro.id;
      }

      const enderecoData = {
        logradouro: logradouroId,
        numero: data.numero,
        complemento: data.complemento,
        ponto_referencia: data.ponto_referencia,
        latitude: data.latitude,
        longitude: data.longitude
      };

      const response = await api.post(this.baseUrl, enderecoData);
      return response.data;
    } catch (error: any) {
      console.error('Erro ao criar endereço:', error);
      throw error;
    }
  }

  async getEndereco(id: number): Promise<Endereco> {
    const response = await api.get(`${this.baseUrl}${id}/`);
    return response.data;
  }

  async updateEndereco(id: number, data: Partial<EnderecoCreate>): Promise<Endereco> {
    const response = await api.patch(`${this.baseUrl}${id}/`, data);
    return response.data;
  }
}

export const enderecoService = new EnderecoService(); 