import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import FuncionarioForm from './funcionarioForm';
import { Funcionario, FuncionarioUpdate } from '../../types/Funcionario';

export const EditarAssinante: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [assinante, setAssinante] = useState<Funcionario | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);



  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#32bef0]"></div>
      </div>
    );
  }

  if (error || !assinante) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  {error || 'Assinante não encontrado'}
                </h3>
                <button
                  onClick={() => navigate('/assinantes')}
                  className="mt-2 text-sm text-red-600 hover:text-red-500"
                >
                  Voltar para lista
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-[#29306a]">
            Editar Assinante
          </h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Atualize os dados do assinante
          </p>
        </div>

        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            
          </div>
        </div>
      </div>
    </div>
  );
}; 