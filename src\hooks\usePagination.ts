import { useState, useCallback, useEffect } from 'react';
import { PaginatedResponse } from '../types/api';
import { CrudService, ListParams } from '../services/crud';

interface UsePaginationOptions {
  initialLimit?: number;
  initialParams?: Partial<ListParams>;
}

interface PaginatedService<T> {
  list?: (params: ListParams) => Promise<PaginatedResponse<T>>;
  listar?: (page: number, limit: number) => Promise<PaginatedResponse<T>>;
}

export function usePagination<T>(
  service: CrudService<T> | PaginatedService<T>,
  options: UsePaginationOptions = {}
) {
  const { initialLimit = 10, initialParams = {} } = options;
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [params, setParams] = useState<ListParams>({
    limit: initialLimit,
    offset: 0,
    ...initialParams,
  });
  const [total, setTotal] = useState(0);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      let response;
      
      if ('list' in service && service.list) {
        response = await service.list(params);
      } else if ('listar' in service && service.listar) {
        const page = Math.floor(params.offset || 0) / (params.limit || initialLimit) + 1;
        response = await service.listar(page, params.limit || initialLimit);
      } else {
        throw new Error('Service must implement either list or listar method');
      }

      setData(response.results);
      setTotal(response.count);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Erro ao carregar dados'));
    } finally {
      setLoading(false);
    }
  }, [service, params, initialLimit]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const setPage = useCallback((page: number) => {
    setParams((prev: ListParams) => ({
      ...prev,
      offset: (page - 1) * (prev.limit || initialLimit),
    }));
  }, [initialLimit]);

  const setPageSize = useCallback((pageSize: number) => {
    setParams((prev: ListParams) => ({
      ...prev,
      limit: pageSize,
      offset: 0,
    }));
  }, []);

  const setSearch = useCallback((search: string) => {
    setParams((prev: ListParams) => ({
      ...prev,
      search,
      offset: 0,
    }));
  }, []);

  const setOrdering = useCallback((ordering: string) => {
    setParams((prev: ListParams) => ({
      ...prev,
      ordering,
      offset: 0,
    }));
  }, []);

  const refresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    total,
    params,
    setPage,
    setPageSize,
    setSearch,
    setOrdering,
    refresh,
    currentPage: Math.floor(params.offset || 0) / (params.limit || initialLimit) + 1,
    totalPages: Math.ceil(total / (params.limit || initialLimit)),
  };
} 