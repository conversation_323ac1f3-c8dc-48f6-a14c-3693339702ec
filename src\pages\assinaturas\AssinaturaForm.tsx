import React, { useState, useEffect } from 'react';
import { AssinaturaCreate, AssinaturaUpdate, RecorrenciaEnum, FormaPagamentoEnum } from '../../types/assinatura';

interface AssinaturaFormProps {
  initialData?: Partial<AssinaturaCreate>;
  onSubmit: (data: AssinaturaCreate | AssinaturaUpdate) => void;
  loading?: boolean;
  isEditing?: boolean;
}

export const AssinaturaForm: React.FC<AssinaturaFormProps> = ({
  initialData,
  onSubmit,
  loading = false,
  isEditing = false
}) => {
  const [formData, setFormData] = useState<AssinaturaCreate>({
    recorrencia: 'mensal',
    forma_pagamento: 'cartao',
    assinante: 0,
    cidade: 0,
    plano: 0
  });

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: ['assinante', 'cidade', 'plano'].includes(name) ? parseInt(value) : value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const recorrencias: { value: RecorrenciaEnum; label: string }[] = [
    { value: 'mensal', label: 'Mensal' },
    { value: 'semestral', label: 'Semestral' },
    { value: 'anual', label: 'Anual' }
  ];

  const formasPagamento: { value: FormaPagamentoEnum; label: string }[] = [
    { value: 'cartao', label: 'Cartão de Crédito' },
    { value: 'boleto', label: 'Boleto' },
    { value: 'pix', label: 'PIX' },
    { value: 'debito', label: 'Débito em Conta' }
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="recorrencia" className="block text-sm font-medium text-[#29306a]">
          Recorrência *
        </label>
        <select
          id="recorrencia"
          name="recorrencia"
          value={formData.recorrencia}
          onChange={handleChange}
          required
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#32bef0] focus:ring-[#32bef0] sm:text-sm"
        >
          {recorrencias.map((recorrencia) => (
            <option key={recorrencia.value} value={recorrencia.value}>
              {recorrencia.label}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label htmlFor="forma_pagamento" className="block text-sm font-medium text-[#29306a]">
          Forma de Pagamento *
        </label>
        <select
          id="forma_pagamento"
          name="forma_pagamento"
          value={formData.forma_pagamento}
          onChange={handleChange}
          required
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#32bef0] focus:ring-[#32bef0] sm:text-sm"
        >
          {formasPagamento.map((forma) => (
            <option key={forma.value} value={forma.value}>
              {forma.label}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label htmlFor="data_expiracao" className="block text-sm font-medium text-[#29306a]">
          Data de Expiração
        </label>
        <input
          type="date"
          id="data_expiracao"
          name="data_expiracao"
          value={formData.data_expiracao || ''}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#32bef0] focus:ring-[#32bef0] sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="assinante" className="block text-sm font-medium text-[#29306a]">
          Assinante *
        </label>
        <input
          type="number"
          id="assinante"
          name="assinante"
          value={formData.assinante}
          onChange={handleChange}
          required
          min="1"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#32bef0] focus:ring-[#32bef0] sm:text-sm"
          placeholder="ID do assinante"
        />
      </div>

      <div>
        <label htmlFor="cidade" className="block text-sm font-medium text-[#29306a]">
          Cidade *
        </label>
        <input
          type="number"
          id="cidade"
          name="cidade"
          value={formData.cidade}
          onChange={handleChange}
          required
          min="1"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#32bef0] focus:ring-[#32bef0] sm:text-sm"
          placeholder="ID da cidade"
        />
      </div>

      <div>
        <label htmlFor="plano" className="block text-sm font-medium text-[#29306a]">
          Plano *
        </label>
        <input
          type="number"
          id="plano"
          name="plano"
          value={formData.plano}
          onChange={handleChange}
          required
          min="1"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#32bef0] focus:ring-[#32bef0] sm:text-sm"
          placeholder="ID do plano"
        />
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={() => window.history.back()}
          className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-[#29306a] hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0]"
        >
          Cancelar
        </button>
        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#32bef0] hover:bg-[#2ba8d8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0] disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              Salvando...
            </>
          ) : (
            isEditing ? 'Atualizar' : 'Criar'
          )}
        </button>
      </div>
    </form>
  );
}; 