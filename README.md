# Tact IMOBI BackOffice

Sistema de gerenciamento para o Tact IMOBI, uma plataforma completa para gestão imobiliária.

## 🚀 Tecnologias

Este projeto foi desenvolvido com as seguintes tecnologias:

- [React](https://reactjs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [React Router DOM](https://reactrouter.com/)
- [React Hook Form](https://react-hook-form.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Axios](https://axios-http.com/)
- [Docker](https://www.docker.com/)

## 💻 Pré-requisitos

Antes de começar, verifique se você tem os seguintes requisitos instalados:

### Desenvolvimento Local
- Node.js (versão 16.x ou superior)
- npm (normalmente vem com o Node.js)

### Usando Docker
- Docker
- Docker Compose

## 🔧 Instalação

### Desenvolvimento Local

1. Clone o repositório:
```bash
git clone [URL_DO_REPOSITÓRIO]
cd tact-frontend-backoffice
```

2. Instale as dependências:
```bash
npm install
```

### Usando Docker

1. Clone o repositório:
```bash
git clone [URL_DO_REPOSITÓRIO]
cd frontend-tact-new
```

2. Para desenvolvimento (com hot-reload):
```bash
docker-compose up web-dev
```

3. Para produção:
```bash
docker-compose up web-prod
```

## ⚙️ Configuração

### Desenvolvimento Local

1. Crie um arquivo `.env` na raiz do projeto:
```env
REACT_APP_API_URL=http://seu-backend-url/api
```

### Usando Docker

As variáveis de ambiente já estão configuradas no `docker-compose.yml`. Se necessário, ajuste os valores para seu ambiente:

```yaml
environment:
  - REACT_APP_API_URL=http://seu-backend-url/api
```

## 🏃‍♂️ Executando o Projeto

### Desenvolvimento Local

1. Para iniciar o servidor de desenvolvimento:
```bash
npm start
```
O aplicativo estará disponível em `http://localhost:3001`

2. Para criar uma build de produção:
```bash
npm run build
```

### Usando Docker

1. Desenvolvimento com hot-reload:
```bash
docker-compose up web-dev
```
O aplicativo estará disponível em `http://localhost:3001`

2. Produção:
```bash
docker-compose up web-prod
```
O aplicativo estará disponível em `http://localhost:80`

3. Construir e forçar reconstrução das imagens:
```bash
docker-compose build --no-cache
```

4. Parar os containers:
```bash
docker-compose down
```

## 📚 Estrutura do Projeto

```
src/
├── components/        # Componentes reutilizáveis
├── hooks/            # Custom hooks
├── pages/            # Componentes de página
├── services/         # Serviços e integrações com API
├── types/            # Definições de tipos TypeScript
└── utils/            # Funções utilitárias
```

## 🔒 Autenticação

O sistema utiliza autenticação baseada em token JWT. O token é armazenado no localStorage e gerenciado pelo `tokenService`.

## 📋 Funcionalidades Principais

- **Autenticação**
  - Login
  - Logout
  - Gerenciamento de tokens (access e refresh)

- **Gerenciamento de Cidades**
  - Listagem com paginação
  - Criação
  - Edição
  - Exclusão

- **Outras Funcionalidades**
  - Assinaturas
  - Empreendimentos
  - Franqueados
  - Incorporadoras
  - Movimentações
  - Planos

## 🔍 Desenvolvimento

### Padrões de Código

- Utilize TypeScript para todas as novas funcionalidades
- Siga as convenções de nomenclatura existentes
- Mantenha os componentes pequenos e focados
- Utilize os hooks personalizados para lógica reutilizável

### Serviços

Os serviços seguem um padrão CRUD base implementado em `CrudService`. Para criar um novo serviço:

```typescript
import { CrudService } from './crud';
import { SeuTipo } from '../types/seuTipo';

class SeuService extends CrudService<SeuTipo> {
  constructor() {
    super('/seu-endpoint/');
  }
}

export const seuService = new SeuService();
```

### Formulários

Utilize o React Hook Form para formulários. Exemplo:

```typescript
const { register, handleSubmit } = useForm<SeuTipo>();

const onSubmit = async (data: SeuTipo) => {
  // Lógica de submissão
};
```

## 🤝 Contribuindo

1. Crie uma branch para sua feature:
```bash
git checkout -b feature/sua-feature
```

2. Commit suas mudanças:
```bash
git commit -m 'feat: Adiciona nova funcionalidade'
```

3. Push para a branch:
```bash
git push origin feature/sua-feature
```

4. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença [sua licença]. Veja o arquivo `LICENSE` para mais detalhes.

## 👥 Autores

- Equipe Tact IMOBI
