import React from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { planoService } from '../../services/planoService';
import { PlanoInput } from '../../types/plano';

interface PlanoFormProps {
  initialData?: PlanoInput;
  isEditing?: boolean;
}

export const PlanoForm: React.FC<PlanoFormProps> = ({ initialData, isEditing }) => {
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<PlanoInput>({
    defaultValues: initialData || {
      nome: '',
      descricao: '',
      valor: ''
    }
  });

  const navigate = useNavigate();
  const { id } = useParams();

  const onSubmit = async (data: PlanoInput) => {
    try {
      if (isEditing && id) {
        await planoService.atualizar(Number(id), data);
        toast.success('Plano atualizado com sucesso!');
      } else {
        await planoService.criar(data);
        toast.success('Plano criado com sucesso!');
      }
      navigate('/planos');
    } catch (error) {
      toast.error('Erro ao salvar plano. Por favor, tente novamente.');
      console.error('Erro ao salvar plano:', error);
    }
  };

  const formatarValor = (event: React.ChangeEvent<HTMLInputElement>) => {
    let valor = event.target.value.replace(/\D/g, '');
    valor = (parseFloat(valor) / 100).toFixed(2);
    event.target.value = valor;
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditing ? 'Editar Plano' : 'Novo Plano'}
        </h1>
        <p className="mt-2 text-sm text-gray-700">
          {isEditing ? 'Atualize os dados do plano' : 'Preencha os dados para criar um novo plano'}
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <label htmlFor="nome" className="block text-sm font-medium text-gray-700">
            Nome
          </label>
          <input
            type="text"
            id="nome"
            {...register('nome', { 
              required: 'Nome é obrigatório',
              maxLength: { value: 255, message: 'Nome deve ter no máximo 255 caracteres' }
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.nome && (
            <p className="mt-1 text-sm text-red-600">{errors.nome.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="descricao" className="block text-sm font-medium text-gray-700">
            Descrição
          </label>
          <textarea
            id="descricao"
            rows={3}
            {...register('descricao', { required: 'Descrição é obrigatória' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.descricao && (
            <p className="mt-1 text-sm text-red-600">{errors.descricao.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="valor" className="block text-sm font-medium text-gray-700">
            Valor (R$)
          </label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 sm:text-sm">R$</span>
            </div>
            <input
              type="text"
              id="valor"
              {...register('valor', { 
                required: 'Valor é obrigatório',
                pattern: {
                  value: /^-?\d{0,6}(?:\.\d{0,2})?$/,
                  message: 'Valor deve ser um número válido (ex: 99.99)'
                }
              })}
              onChange={formatarValor}
              placeholder="0,00"
              className="pl-12 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
          {errors.valor && (
            <p className="mt-1 text-sm text-red-600">{errors.valor.message}</p>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/planos')}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmitting ? 'Salvando...' : isEditing ? 'Atualizar' : 'Criar'}
          </button>
        </div>
      </form>
    </div>
  );
}; 