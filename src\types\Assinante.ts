export interface Assinante {
  id: number;
  nome_completo: string;
  email: string;
  bloqueado: boolean;
  tipo_plano?: string; // To filter by plan type
}

export interface AssinanteCreate {
  nome_completo: string;
  email: string;
  password: string;
  // Add other fields as needed for creation
}

export interface AssinanteUpdate {
  nome_completo?: string;
  email?: string;
  bloqueado?: boolean;
  // Não inclua password aqui, a menos que seja para uma funcionalidade de "mudar senha" separada
}

// Nova definição para o tipo de dados do formulário
// Este tipo representa os dados que o formulário pode conter.
// 'password' é opcional porque não é sempre preenchido (ex: edição).
export interface AssinanteFormFields {
  nome_completo: string;
  email: string;
  password?: string;
  bloqueado?: boolean;
}

export interface PaginatedAssinanteList {
  count: number;
  next?: string;
  previous?: string;
  results: Assinante[];
}