import React, { useEffect } from 'react';
import { IncorporadoraForm } from './IncorporadoraForm';
import { authService } from '../../services/auth';
import { tokenService } from '../../services/tokenService';

export const NovaIncorporadora: React.FC = () => {
  useEffect(() => {
    // Debug: verificar se o usuário está autenticado
    console.log('=== DEBUG AUTENTICAÇÃO ===');
    console.log('Está autenticado:', authService.isAuthenticated());
    console.log('Token:', tokenService.getToken());
    console.log('Headers da API:', authService.getToken() ? `Bearer ${authService.getToken()?.access}` : 'Sem token');
    console.log('========================');
  }, []);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-[#29306a]">Nova Incorporadora</h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Cadastre uma nova incorporadora no sistema
          </p>
        </div>
        <IncorporadoraForm />
      </div>
    </div>
  );
}; 