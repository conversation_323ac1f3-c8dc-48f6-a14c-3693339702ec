export interface UserLogin {
  login: string;
  password: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface UserRegister {
  email: string;
  password: string;
  nome_completo: string;
  codigovinculacao: string;
  tipo:string;
}

export interface FranquiaMembership {
    franquia_id: number;
    franquia_nome: string;
    cargos: string[];
    status: string;
}

export interface User {
  id: number;
  email: string;
  nome_completo: string;
  cpf: string | null;
  data_nascimento?: string | null;
  genero?: string | null;
  telefones: any[];
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  created: string;
  modified: string;
  last_login?: string;
  codigovinculacao: string;
  status_vinculo: string;
  tipo?: string;
  franquia_membership: FranquiaMembership | null;
  empresa_membership: any | null;
}

export interface UserProfile {
  nome_completo: string;
  cpf: string;
  data_nascimento?: string;
  genero?: string;
}

export interface ChangePassword {
  old_password: string;
  password: string;
  password_confirm: string;
}

export interface TokenResponse {
  refresh: string;
  access: string;
}

export interface AuthResponse {
  detail: string;
  token: TokenResponse;
}

export interface AuthState {
  user: User | null;
  token: TokenResponse | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}