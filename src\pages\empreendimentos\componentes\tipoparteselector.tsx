import React, { useState, useEffect } from 'react';
import Select from 'react-select'; // Importa o componente do React Select
import GerenciarTiposParteEmpreendimento from '../GerenciarTiposParteEmpreendimento';
import { TipoParteEmpreendimento } from '../../../types/empreendimento';
import { TipoParteEmpreendimentoService } from '../../../services/empreendimentoService';
import { Plus } from 'lucide-react';
import {authService} from '../../../services/auth'
import {User} from '../../../types/auth'





const inputStyle = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm";

interface TipoParteSelectorProps {
    label: string;
    value: (string | number)[];
    onChange: (value: (string | number)[]) => void;
}

const TipoParteSelector: React.FC<TipoParteSelectorProps> = ({ label, value, onChange }) => {
    const [showModal, setShowModal] = useState(false);
    const [tiposParte, setTiposParte] = useState<TipoParteEmpreendimento[]>([]);
    const [loading, setLoading] = useState(true);
    const [user, setUser] = useState<User | null>(null);

    useEffect(() => {
        async function fetchUser() {
        try {
            const userProfile = await authService.getProfile();
            setUser(userProfile); // atualiza o state
        } catch (error) {
            console.error("Erro ao buscar perfil do usuário", error);
        }
        }

        fetchUser();
    }, []);
    const tipoParteEmpreendimentoService = new TipoParteEmpreendimentoService();

    const loadTiposParte = async () => {
        setLoading(true);
        try {
            const data = await tipoParteEmpreendimentoService.getTipos();
            setTiposParte(data);
        } catch (error) {
            console.error("Falha ao carregar os tipos de parte:", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadTiposParte();
    }, []);

    const handleModalClose = async (selectedTipo?: TipoParteEmpreendimento) => {
        setShowModal(false);
        await loadTiposParte();
        
        if (selectedTipo) {
            const currentSelectedIds = Array.isArray(value) ? value : [];
            const newId = Number(selectedTipo.id);
            if (!currentSelectedIds.includes(newId)) {
                onChange([...currentSelectedIds, newId]);
            }
        }
    };

    // A função de mudança é simplificada, pois o React-Select já retorna uma lista de objetos
    const handleSelectChange = (selectedOptions: any) => {
        if (!selectedOptions) {
            onChange([]);
            return;
        }
        const selectedIds = selectedOptions.map((option: any) => option.value);
        onChange(selectedIds);
    };

    // Mapeia os dados da API para o formato que o React-Select entende
    const options = tiposParte.map(tipo => ({
        value: tipo.id,
        label: tipo.Nome_tipo_parte_empreendimento,
    }));
    
    // Mapeia os valores selecionados para o formato que o React-Select entende
    const selectedValues = options.filter(option => value.includes(option.value));
    
    return (
        <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
                {label} *
            </label>
            <div className="flex space-x-2">
                <div className="flex-1">
                    <Select
                        isMulti // Permite a seleção de múltiplos itens
                        options={options}
                        value={selectedValues}
                        onChange={handleSelectChange}
                        placeholder="Pesquisar e selecionar tipos..."
                        isDisabled={loading}
                        className="react-select-container"
                        classNamePrefix="react-select"
                    />
                </div>
                {(user?.tipo == 'TACT') ?( <button
                    type="button"
                    onClick={() => setShowModal(true)}
                    className="px-3 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 text-xl font-bold flex items-center justify-center"
                    disabled={loading}
                >
                    <Plus size={20} />
                </button>): null}
            </div>
            
            {showModal && (
                <GerenciarTiposParteEmpreendimento 
                    onClose={() => handleModalClose()}
                    onTipoSelected={handleModalClose}
                />
            )}
        </div>
    );
};

export default TipoParteSelector;