import api from './api';
import { Assinatura, AssinaturaCreate, AssinaturaUpdate, PaginatedAssinaturaList } from '../types/assinatura';

const BASE_URL = '/api/assinaturas/';

export const assinaturaService = {
  async listar(limit?: number, offset?: number): Promise<PaginatedAssinaturaList> {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());
    
    const response = await api.get(`${BASE_URL}?${params.toString()}`);
    return response.data;
  },

  async buscarPorId(id: number): Promise<Assinatura> {
    const response = await api.get(`${BASE_URL}${id}/`);
    return response.data;
  },

  async criar(data: AssinaturaCreate): Promise<Assinatura> {
    const response = await api.post(BASE_URL, data);
    return response.data;
  },

  async atualizar(id: number, data: AssinaturaUpdate): Promise<Assinatura> {
    const response = await api.put(`${BASE_URL}${id}/`, data);
    return response.data;
  },

  async atualizarParcial(id: number, data: Partial<AssinaturaUpdate>): Promise<Assinatura> {
    const response = await api.patch(`${BASE_URL}${id}/`, data);
    return response.data;
  },

  async excluir(id: number): Promise<void> {
    await api.delete(`${BASE_URL}${id}/`);
  }
}; 