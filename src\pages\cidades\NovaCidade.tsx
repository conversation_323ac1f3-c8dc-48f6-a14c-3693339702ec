import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { cidadeService } from '../../services/cidadeService';
import { useForm } from 'react-hook-form';

interface CidadeFormData {
  nome: string;
  estado: string;
  uf: string;
  pais: string;
}

export const NovaCidade: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CidadeFormData>();

  const onSubmit = async (data: CidadeFormData) => {
    setLoading(true);
    setError(null);

    try {
      await cidadeService.criar(data);
      navigate('/cidades');
    } catch (err) {
      console.error('Erro ao criar cidade:', err);
      setError('Erro ao criar cidade. Por favor, tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="md:grid md:grid-cols-3 md:gap-6">
        <div className="md:col-span-1">
          <div className="px-4 sm:px-0">
            <h3 className="text-lg font-medium leading-6 text-[#29306a]">
              Nova Cidade
            </h3>
            <p className="mt-1 text-sm text-[#64748b]">
              Preencha os dados abaixo para cadastrar uma nova cidade.
            </p>
          </div>
        </div>

        <div className="mt-5 md:mt-0 md:col-span-2">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="shadow sm:rounded-md sm:overflow-hidden">
              <div className="px-4 py-5 bg-white space-y-6 sm:p-6">
                {error && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">{error}</h3>
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <label
                    htmlFor="nome"
                    className="block text-sm font-medium text-[#29306a]"
                  >
                    Nome
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      id="nome"
                      {...register('nome', { required: 'Nome é obrigatório' })}
                      className="shadow-sm focus:ring-[#32bef0] focus:border-[#32bef0] block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                    {errors.nome && (
                      <p className="mt-1 text-sm text-red-600">{errors.nome.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="estado"
                    className="block text-sm font-medium text-[#29306a]"
                  >
                    Estado
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      id="estado"
                      {...register('estado', { required: 'Estado é obrigatório' })}
                      className="shadow-sm focus:ring-[#32bef0] focus:border-[#32bef0] block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                    {errors.estado && (
                      <p className="mt-1 text-sm text-red-600">{errors.estado.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="uf"
                    className="block text-sm font-medium text-[#29306a]"
                  >
                    UF
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      id="uf"
                      {...register('uf', {
                        required: 'UF é obrigatória',
                        maxLength: {
                          value: 2,
                          message: 'UF deve ter 2 caracteres',
                        },
                      })}
                      className="shadow-sm focus:ring-[#32bef0] focus:border-[#32bef0] block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                    {errors.uf && (
                      <p className="mt-1 text-sm text-red-600">{errors.uf.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="pais"
                    className="block text-sm font-medium text-[#29306a]"
                  >
                    País
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      id="pais"
                      {...register('pais', { required: 'País é obrigatório' })}
                      className="shadow-sm focus:ring-[#32bef0] focus:border-[#32bef0] block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                    {errors.pais && (
                      <p className="mt-1 text-sm text-red-600">{errors.pais.message}</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                <button
                  type="button"
                  onClick={() => navigate('/cidades')}
                  className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-[#64748b] bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0] mr-3"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${
                    loading
                      ? 'bg-[#32bef0] opacity-50 cursor-not-allowed'
                      : 'bg-[#32bef0] hover:bg-[#2ba8d8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0]'
                  }`}
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
                  ) : (
                    'Salvar'
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}; 