import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { empreendimentoService } from '../../services/empreendimentoService';
import { Empreendimento, EmpreendimentoCreate } from '../../types/empreendimento';
import EmpreendimentoFormBasico from './EmpreendimentoFormBasico';

const EditarEmpreendimento: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [empreendimento, setEmpreendimento] = useState<Empreendimento | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEmpreendimento = async () => {
      try {
        setLoading(true);
        const data = await empreendimentoService.get(parseInt(id!));
        setEmpreendimento(data);
      } catch (error) {
        console.error('Erro ao buscar empreendimento:', error);
        alert('Erro ao carregar empreendimento');
        navigate('/empreendimentos');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchEmpreendimento();
    }
  }, [id, navigate]);

  const handleSubmit = async (data: EmpreendimentoCreate) => {
    try {
      await empreendimentoService.update(parseInt(id!), data);
      alert('Empreendimento atualizado com sucesso!');
      navigate('/empreendimentos');
    } catch (error) {
      console.error('Erro ao atualizar empreendimento:', error);
      alert('Erro ao atualizar empreendimento');
    }
  };

  // Converter Empreendimento para EmpreendimentoCreate
const convertToFormData = (emp: Empreendimento): Partial<EmpreendimentoCreate> => {
  return {
    nome: emp.nome,
    incorporadora: emp.incorporadora,
    data_entrega: emp.data_entrega,
    data_inicio_comercializacao: emp.data_inicio_comercializacao,
    observacao: emp.observacao,
    area_total: emp.area_total,
    area_construida: emp.area_construida,
    is_lancamento: emp.is_lancamento,
    endereco: {
      logradouro: {
        cidade: emp.endereco.logradouro.cidade.id,
        zipcode: emp.endereco.logradouro.zipcode,
        nome: emp.endereco.logradouro.nome,
        bairro: emp.endereco.logradouro.bairro
      },
      numero: emp.endereco.numero,
      complemento: emp.endereco.complemento || '',
      ponto_referencia: emp.endereco.ponto_referencia || '',
      latitude: emp.endereco.latitude,
      longitude: emp.endereco.longitude
    }
  };
};

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!empreendimento) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Empreendimento não encontrado</h2>
          <button
            onClick={() => navigate('/empreendimentos')}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Voltar para Lista
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">
            Editar Empreendimento: {empreendimento.nome}
          </h1>
        </div>
      </div>

      <div className="py-8">
        <EmpreendimentoFormBasico
          onNext={handleSubmit}
          initialData={convertToFormData(empreendimento)}
        />
      </div>
    </div>
  );
};

export default EditarEmpreendimento; 