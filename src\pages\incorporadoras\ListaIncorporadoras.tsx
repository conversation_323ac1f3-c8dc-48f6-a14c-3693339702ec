import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { incorporadoraService } from '../../services/incorporadoraService';
import { Incorporadora } from '../../types/incorporadora';

export const ListaIncorporadoras: React.FC = () => {
  const [incorporadoras, setIncorporadoras] = useState<Incorporadora[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchIncorporadoras = async () => {
      try {
        const response = await incorporadoraService.listar();
        setIncorporadoras(response.results);
      } catch (err) {
        console.error('Erro ao carregar incorporadoras:', err);
        setError('Erro ao carregar a lista de incorporadoras');
      } finally {
        setLoading(false);
      }
    };

    fetchIncorporadoras();
  }, []);

  const handleDelete = async (id: number) => {
    if (!window.confirm('Tem certeza que deseja excluir esta incorporadora?')) {
      return;
    }

    try {
      await incorporadoraService.excluir(id);
      setIncorporadoras(incorporadoras.filter(incorporadora => incorporadora.id !== id));
    } catch (err) {
      console.error('Erro ao excluir incorporadora:', err);
      setError('Erro ao excluir a incorporadora');
    }
  };

  const formatarCNPJ = (cnpj: string | undefined | null) => {
    if (!cnpj) return '-';
    return cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#32bef0]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">{error}</h3>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-[#29306a]">Incorporadoras</h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Lista de todas as incorporadoras cadastradas no sistema
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link
            to="/incorporadoras/novo"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-[#32bef0] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#2ba8d8] focus:outline-none focus:ring-2 focus:ring-[#32bef0] focus:ring-offset-2 sm:w-auto"
          >
            Adicionar incorporadora
          </Link>
        </div>
      </div>

      <div className="mt-8 flex flex-col">
        <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-[#29306a] sm:pl-6"
                    >
                      ID
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-[#29306a]"
                    >
                      Nome Fantasia
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-[#29306a]"
                    >
                      Razão Social
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-[#29306a]"
                    >
                      CNPJ
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-[#29306a]"
                    >
                      Data Criação
                    </th>
                    <th
                      scope="col"
                      className="relative py-3.5 pl-3 pr-4 sm:pr-6"
                    >
                      <span className="sr-only">Ações</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {incorporadoras.map((incorporadora) => (
                    <tr key={incorporadora.id}>
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-[#29306a] sm:pl-6">
                        {incorporadora.id}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#64748b]">
                        {incorporadora.empresa?.nome_fantasia || '-'}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#64748b]">
                        {incorporadora.empresa?.razao_social || '-'}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#64748b]">
                        {formatarCNPJ(incorporadora.empresa?.cnpj)}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#64748b]">
                        {incorporadora.created 
                          ? new Date(incorporadora.created).toLocaleDateString('pt-BR')
                          : '-'
                        }
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <Link
                          to={`/incorporadoras/${incorporadora.id}/editar`}
                          className="text-[#32bef0] hover:text-[#2ba8d8] mr-4"
                        >
                          Editar
                        </Link>
                        <button
                          onClick={() => handleDelete(incorporadora.id)}
                          className="text-[#f55eeb] hover:text-[#e54dd8]"
                        >
                          Excluir
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 