export interface Plano {
  id: number;
  nome: string;
  descricao: string;
  valor: string;
  created: string;
  modified: string;
}

export interface PlanoInput {
  nome: string;
  descricao: string;
  valor: string;
}

export interface PlanoUpdateInput extends Partial<PlanoInput> {}

export interface PaginatedPlanoResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Plano[];
} 