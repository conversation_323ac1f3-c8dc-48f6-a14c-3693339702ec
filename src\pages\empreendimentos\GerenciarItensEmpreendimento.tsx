import React, { useState, useEffect } from 'react';
import { ItemEmpreendimentoService } from '../../services/empreendimentoService';
import { ItemEmpreendimento, ItemEmpreendimentoCreate } from '../../types/empreendimento';

interface GerenciarItensEmpreendimentoProps {
  onClose: () => void;
  onItensSelected?: (itens: ItemEmpreendimento[]) => void;
}

const GerenciarItensEmpreendimento: React.FC<GerenciarItensEmpreendimentoProps> = ({
  onClose,
  onItensSelected
}) => {
  const [itens, setItens] = useState<ItemEmpreendimento[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<ItemEmpreendimento | null>(null);
  const [formData, setFormData] = useState<ItemEmpreendimentoCreate>({
    name: '',
    tipo_item_empreendimento: 1,
    descricao: ''
  });

  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({
    lazer: '',
    seguranca: '',
    infraestrutura: '',
  });

  const itemEmpreendimentoService = new ItemEmpreendimentoService();

  const loadItens = async () => {
    const itensData = await itemEmpreendimentoService.getAllItens();
    setItens(itensData);
  };

  useEffect(() => {
    loadItens();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      alert('Nome é obrigatório');
      return;
    }

    if (editingItem) {
      await itemEmpreendimentoService.updateItem(editingItem.id, formData);
    } else {
      await itemEmpreendimentoService.addItem(formData);
    }

    setFormData({ name: '', tipo_item_empreendimento: 1, descricao: '' });
    setEditingItem(null);
    setShowForm(false);
    loadItens();
  };

  const handleEdit = (item: ItemEmpreendimento) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      tipo_item_empreendimento: item.tipo_item_empreendimento,
      descricao: item.descricao || ''
    });
    setShowForm(true);
  };

  const deleteItem = async (id: string) => {
    if (window.confirm('Tem certeza que deseja excluir este item?')) {
      await itemEmpreendimentoService.deleteItem(id);
      loadItens();
    }
  };

  const handleSearchChange = (categoria: string, value: string) => {
    setSearchTerms(prev => ({ ...prev, [categoria]: value }));
  };

  const CategoriaNumeroParaTexto: Record<number, string> = {
    1: "lazer",
    2: "seguranca",
    3: "infraestrutura"
  };

  const categoriaLabels: Record<string, string> = {
    'lazer': 'Lazer',
    'seguranca': 'Segurança',
    'infraestrutura': 'Infraestrutura'
  };

  const categoriaColors: Record<string, string> = {
    'lazer': 'border-blue-300 bg-blue-50',
    'seguranca': 'border-red-300 bg-red-50',
    'infraestrutura': 'border-green-300 bg-green-50'
  };
  
  const categorias = Object.entries(CategoriaNumeroParaTexto).map(([key, value]) => ({
    id: Number(key),
    name: value,
  }));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center mb-6 flex-shrink-0">
          <h2 className="text-xl font-semibold text-gray-900">
            Gerenciar Itens do Empreendimento
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">✕</button>
        </div>

        {!showForm ? (
          <div className="flex-grow overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Itens Cadastrados</h3>
              <button
                onClick={() => {
                  setEditingItem(null);
                  setFormData({ name: '', tipo_item_empreendimento: 1, descricao: '' });
                  setShowForm(true);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Novo Item
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {categorias.map((categoria) => {
                const filteredItems = itens.filter(item =>
                  item.tipo_item_empreendimento === categoria.id &&
                  item.name.toLowerCase().includes(searchTerms[categoria.name].toLowerCase())
                );

                return (
                  <div 
                    key={categoria.id} 
                    className={`border rounded-lg p-4 ${categoriaColors[categoria.name]} flex flex-col h-[40vh]`}
                  >
                    <h4 className="font-medium text-gray-900 mb-2 flex-shrink-0">
                      {categoriaLabels[categoria.name]}
                    </h4>

                    <input
                      type="text"
                      placeholder="Pesquisar..."
                      value={searchTerms[categoria.name]}
                      onChange={(e) => handleSearchChange(categoria.name, e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded-md mb-3 text-sm flex-shrink-0"
                    />

                    {/* Lista com scroll dentro do quadrado */}
                    <div className="space-y-2 flex-grow overflow-y-auto pr-2">
                      {filteredItems.length > 0 ? (
                        filteredItems.map((item) => (
                          <div key={item.id} className="flex justify-between items-center p-2 bg-white rounded shadow-sm">
                            <span className="text-sm text-gray-800">{item.name}</span>
                            <div className="flex space-x-1">
                              <button
                                onClick={() => handleEdit(item)}
                                className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                              >
                                Editar
                              </button>
                              <button
                                onClick={() => deleteItem(item.id)}
                                className="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
                              >
                                Excluir
                              </button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <p className="text-gray-500 text-sm text-center pt-4">Nenhum item encontrado.</p>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6 flex-grow">
            <h3 className="text-lg font-medium text-gray-900">
              {editingItem ? 'Editar Item' : 'Novo Item'}
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Nome *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: Piscina, Academia"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Categoria *</label>
              <select
                value={formData.tipo_item_empreendimento}
                onChange={(e) => setFormData(prev => ({ ...prev, tipo_item_empreendimento: Number(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value={1}>Lazer</option>
                <option value={2}>Segurança</option>
                <option value={3}>Infraestrutura</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
              <textarea
                value={formData.descricao}
                onChange={(e) => setFormData(prev => ({ ...prev, descricao: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Descrição do item"
                rows={3}
              />
            </div>
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingItem(null);
                }}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {editingItem ? 'Atualizar' : 'Criar'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default GerenciarItensEmpreendimento;
