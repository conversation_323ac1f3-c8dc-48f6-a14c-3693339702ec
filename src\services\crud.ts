import api from './api';
import { PaginatedResponse } from '../types/api';

export interface ListParams {
  limit?: number;
  offset?: number;
  search?: string;
  ordering?: string;
}

export class CrudService<T> {
  constructor(private endpoint: string) {}

  async list(params?: ListParams): Promise<PaginatedResponse<T>> {
    const response = await api.get<PaginatedResponse<T>>(this.endpoint, { params });
    return response.data;
  }

  async get(id: number): Promise<T> {
    const response = await api.get<T>(`${this.endpoint}${id}/`);
    return response.data;
  }

  async create(data: Omit<T, 'id'>): Promise<T> {
    const response = await api.post<T>(this.endpoint, data);
    return response.data;
  }

  async update(id: number, data: Partial<T>): Promise<T> {
    const response = await api.patch<T>(`${this.endpoint}${id}/`, data);
    return response.data;
  }

  async delete(id: number): Promise<void> {
    await api.delete(`${this.endpoint}${id}/`);
  }
} 