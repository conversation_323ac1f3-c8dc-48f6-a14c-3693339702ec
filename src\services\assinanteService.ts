import api from './api';
import { Assinante, AssinanteCreate, AssinanteUpdate, PaginatedAssinanteList } from '../types/Assinante';

const BASE_URL = '/assinantes/'; // Define a base URL para os endpoints de assinantes

export const assinanteService = {
  /**
   * Lista assinantes com paginação e filtros.
   * Certifique-se de que o backend implementa os filtros de busca, tipoPlano e bloqueado.
   */
  async listar(
    limit: number,
    offset: number,
    search?: string, // Para nome_completo ou email
    tipoPlano?: string, // Para filtrar por tipo de plano
    bloqueado?: boolean, // Para filtrar por status de bloqueio
  ): Promise<PaginatedAssinanteList> {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());

    if (search) params.append('search', search);
    if (tipoPlano) params.append('tipo_plano', tipoPlano);
    if (bloqueado !== undefined) params.append('bloqueado', bloqueado.toString());

    // Este é o ponto de integração com o endpoint GET /assinantes/
    const response = await api.get(`${BASE_URL}?${params.toString()}`);
    return response.data;
  },

  /**
   * Cria um novo assinante.
   * Certifique-se de que o backend implementa o endpoint POST /assinantes/ para criação.
   */
  async criar(data: AssinanteCreate): Promise<Assinante> {
    // Este é o ponto de integração com o endpoint POST /assinantes/
    const response = await api.post(BASE_URL, data);
    return response.data;
  },

  /**
   * Atualiza um assinante existente (PUT).
   * Certifique-se de que o backend implementa o endpoint PUT /assinantes/{id}/ para atualização completa.
   */
  async atualizar(id: number, data: AssinanteUpdate): Promise<Assinante> {
    // Este é o ponto de integração com o endpoint PUT /assinantes/{id}/
    const response = await api.put(`${BASE_URL}${id}/`, data);
    return response.data;
  },

  /**
   * Atualiza parcialmente um assinante existente (PATCH).
   * Certifique-se de que o backend implementa o endpoint PATCH /assinantes/{id}/ para atualização parcial.
   */
  async atualizarParcial(id: number, data: Partial<AssinanteUpdate>): Promise<Assinante> {
    // Este é o ponto de integração com o endpoint PATCH /assinantes/{id}/
    const response = await api.patch(`${BASE_URL}${id}/`, data);
    return response.data;
  },

  /**
   * Exclui um assinante.
   * Certifique-se de que o backend implementa o endpoint DELETE /assinantes/{id}/ para exclusão.
   */
  async excluir(id: number): Promise<void> {
    // Este é o ponto de integração com o endpoint DELETE /assinantes/{id}/
    await api.delete(`${BASE_URL}${id}/`);
  },

  /**
   * Bloqueia um assinante.
   * Certifique-se de que o backend implementa o endpoint POST /assinantes/{id}/bloquear/.
   */
  async bloquear(id: number): Promise<Assinante> {
    // Este é o ponto de integração com o endpoint POST /assinantes/{id}/bloquear/
    const response = await api.post(`${BASE_URL}${id}/bloquear/`);
    return response.data;
  },

  /**
   * Desbloqueia um assinante.
   * Certifique-se de que o backend implementa o endpoint POST /assinantes/{id}/desbloquear/.
   */
  async desbloquear(id: number): Promise<Assinante> {
    // Este é o ponto de integração com o endpoint POST /assinantes/{id}/desbloquear/
    const response = await api.post(`${BASE_URL}${id}/desbloquear/`);
    return response.data;
  },

  /**
   * Busca um assinante por ID.
   * Certifique-se de que o backend implementa o endpoint GET /assinantes/{id}/.
   */
  async buscarPorId(id: number): Promise<Assinante> {
    // Este é o ponto de integração com o endpoint GET /assinantes/{id}/
    const response = await api.get(`${BASE_URL}${id}/`);
    return response.data;
  },
};