import { Endereco } from './empresa';

export interface Empresa {
  id: number;
  endereco: Endereco;
  razao_social: string;
  nome_fantasia: string;
  cnpj: string;
  created: string;
  modified: string;
}

export interface Incorporadora {
  id: number;
  empresa: Empresa; // Dados completos na leitura (GET), apenas ID na escrita (POST)
  created: string;
  modified: string;
}

export interface EmpresaInput {
  endereco: number; // ID do endereço
  razao_social: string;
  nome_fantasia: string;
  cnpj: string;
}

export interface IncorporadoraInput {
  empresa: number; // ID da empresa
}

export interface IncorporadoraUpdateInput extends Partial<IncorporadoraInput> {}

export interface PaginatedIncorporadoraResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Incorporadora[];
}

export interface PaginatedEmpresaResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Empresa[];
} 