import { End<PERSON><PERSON>, Logradouro, Cidade } from './empresa';

export interface Empreendimento {
  id: number;
  imagem_empreendimento:string;
  tabela_preco: string;
  endereco: Endereco;
  fase_empreendimento?: number; // writeOnly
  tipo_empreendimento?: number[]; // writeOnly
  item_empreendimento?: string[]; // writeOnly
  fase_atual?: number; // readOnly
  tipo_empreendimento_descricao?: string; // readOnly
  itens_empreendimento_descricoes?: string[]; // readOnly
  created: string;
  modified: string;
  nome: string;
  area_total:number;
  area_construida:number;
  is_lancamento: boolean;
  data_entrega?: string;
  data_inicio_comercializacao?: string;
  observacao?: string;
  heading?: string;
  pitch?: string;
  incorporadora: number;
}

export interface EmpreendimentoCreate {
  endereco: number | EnderecoCreate;
  fase_empreendimento?: number;
  tabela_preco: string;
  imagem_empreendimento:File;
  tipo_empreendimento?: number[];
  item_empreendimento?: string[];
  area_total:number;
  area_construida:number;
  is_lancamento: boolean;
  nome: string;
  data_entrega?: string;
  data_inicio_comercializacao?: string;
  observacao?: string;
  heading?: string;
  pitch?: string;
  incorporadora: number;
}

export interface EmpreendimentoUpdate {
  endereco?: number | EnderecoCreate;
  fase_empreendimento?: number;
  tipo_empreendimento?: number[];
  item_empreendimento?: string[];
  nome?: string;
  quantidade_garagens?: number;
  data_entrega?: string;
  data_inicio_comercializacao?: string;
  observacao?: string;
  heading?: string;
  pitch?: string;
  incorporadora?: number;
}

export interface PaginatedEmpreendimentoResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Empreendimento[];
}

// Novas interfaces para gerenciamento dinâmico
export interface TipoEmpreendimento {
  id: number;
  Nome_empreendimento: string;
  descricao_empreendimento?: string;
}


export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface ItemEmpreendimento {
  id: string;
  name: string;
  tipo_item_empreendimento: number;
  categoria: number;
  descricao?: string;
  created: string;
  modified: string;
}

export interface TipoEmpreendimentoCreate {
  nome: string;
  descricao: string;
}

export interface TipoParteEmpreendimento {
  id: string;
  Nome_tipo_parte_empreendimento: string;
  descricao?: string;
  atributos_definidos: Record<string, any>;
  created: string;
  modified: string;
}

export interface TipoParteEmpreendimentoCreate {
  Nome_tipo_parte_empreendimento: string;
  descricao?: string;
  atributos_definidos: Record<string, any>;
}

export interface ItemEmpreendimentoCreate {
  name: string;
  tipo_item_empreendimento: number;
  descricao?: string;
}

// Enums existentes
export enum FaseEmpreendimentoEnum {
  PLANTA = 1,
  OBRAS = 2,
  PRONTO = 3
}

export enum AnoDisponibilidadeEnum {
  ANO_2024 = 2024,
  ANO_2025 = 2025,
  ANO_2026 = 2026,
  ANO_2027 = 2027,
  ANO_2028 = 2028,
  ANO_2029 = 2029,
  ANO_2030 = 2030
}

export enum MesDisponibilidadeEnum {
  JANEIRO = 1,
  FEVEREIRO = 2,
  MARCO = 3,
  ABRIL = 4,
  MAIO = 5,
  JUNHO = 6,
  JULHO = 7,
  AGOSTO = 8,
  SETEMBRO = 9,
  OUTUBRO = 10,
  NOVEMBRO = 11,
  DEZEMBRO = 12
}

export enum OrigemRecursoEnum {
  PROPRIO = 1,
  SFH = 2,
  CONDOMINIO = 3,
  COOPERATIVA = 4,
  PROPRIO_SFH = 5,
  OUTROS = 6,
  MCMV = 7
}

export enum TipoParteEmpreendimentoEnum {
  APARTAMENTO = 1,
  CASA = 2,
  LOJA = 3,
  ESCRITORIO = 4,
  GALPAO = 5
}

export interface ParteEmpreendimento {
  id: number;
  torre:number;
  was_lancamento: boolean;
  ano_disponibilidade: AnoDisponibilidadeEnum;
  mes_disponibilidade: MesDisponibilidadeEnum;
  origem_recurso: OrigemRecursoEnum;
  created: string;
  modified: string;
  nome: string;
  area_privativa_m2: string;
  valor_imovel: string;
  forma_pagamento: string;
  atributos_extras: Record<string, any>;
  empreendimento: number;
  tipo_parte_empreendimento: number;
  disponibilidade: number;
  quantidade_garagem: number;
}

export interface ParteEmpreendimentoCreate {
  torre:number;
  was_lancamento: boolean;
  ano_disponibilidade: number; // Mudado de AnoDisponibilidadeEnum para number
  mes_disponibilidade: number; // Mudado de MesDisponibilidadeEnum para number
  origem_recurso: number; // Mudado de OrigemRecursoEnum para number
  nome: string;
  area_privativa_m2: string;
  valor_imovel: string;
  forma_pagamento: string;
  atributos_extras: Record<string, any>;
  empreendimento: number;
  tipo_parte_empreendimento: number;
  disponibilidade: number;
  quantidade_garagem: number;
  mcmv:boolean;
  tipo_produto: number;
}

// Interfaces para endereço
export interface EnderecoCreate {
  logradouro: {
    cidade: number;
    zipcode: string;
    nome: string;
    bairro: string;
  };
  numero?: number;
  complemento?: string;
  ponto_referencia?: string;
  latitude?: string;
  longitude?: string;
} 