import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import EmpreendimentoFormBasico from './EmpreendimentoFormBasico';
import EmpreendimentoFormEspecifico from './EmpreendimentoFormEspecifico';
import { EmpreendimentoCreate } from '../../types/empreendimento';
import { empreendimentoService } from '../../services/empreendimentoService';
import { authService } from '../../services/auth';
import { tokenService } from '../../services/tokenService';

const NovoEmpreendimento: React.FC = () => {
  const [step, setStep] = useState(1);
  const [basicData, setBasicData] = useState<EmpreendimentoCreate | null>(null);
  const [specificData, setSpecificData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Verificar se o usuário está autenticado
    if (!authService.isAuthenticated()) {
      navigate('/login');
    }
  }, [navigate]);

  const handleBasicSubmit = (data: EmpreendimentoCreate) => {
    console.log('=== AVANÇANDO PARA STEP 2 ===');
    console.log('Dados básicos:', data);
    console.log('Dados específicos preservados:', specificData);
    
    setBasicData(data);
    setStep(2);
  };

  const handleBack = (specificDataFromForm?: any) => {
    console.log('=== VOLTANDO PARA STEP 1 ===');
    console.log('Dados básicos preservados:', basicData);
    console.log('Dados específicos preservados:', specificData);
    console.log('Dados específicos do formulário:', specificDataFromForm);
    console.log('Tipo do endereço:', typeof basicData?.endereco);
    if (basicData?.endereco && typeof basicData.endereco === 'object') {
      console.log('Endereço como objeto:', basicData.endereco);
    }
    
    // Se recebeu dados específicos do formulário, preservar
    if (specificDataFromForm) {
      console.log('=== PRESERVANDO DADOS ESPECÍFICOS DO FORMULÁRIO ===');
      console.log('Dados específicos recebidos:', specificDataFromForm);
      setSpecificData(specificDataFromForm);
    }
    
    setStep(1);
  };

  const handleSpecificSubmit = async (specificData: any) => {
    if (!basicData) return;

    // Preservar dados específicos para navegação
    console.log('=== ANTES DE PRESERVAR DADOS ESPECÍFICOS ===');
    console.log('Dados específicos recebidos:', specificData);
    console.log('Estado atual specificData:', specificData);
    
    setSpecificData(specificData);
    console.log('=== DADOS ESPECÍFICOS PRESERVADOS ===');
    console.log('Dados específicos:', specificData);

    setError(null);

    try {
      console.log('=== INICIANDO CRIAÇÃO DO EMPREENDIMENTO ===');
      console.log('Dados básicos:', basicData);
      console.log('Dados específicos:', specificData);

      // Combinar dados básicos e específicos
      const completeData = {
        ...basicData,
        ...specificData
      };

      // Extrair partes do empreendimento dos dados específicos
      const partes = specificData.partes_empreendimento || [];


      

      console.log('Tipo:', completeData.tipo_empreendimento);


      // Usar o novo método que cria empreendimento e partes em sequência
      const empreendimento = await empreendimentoService.createWithParts(completeData, partes);
      
      
      navigate('/empreendimentos');
      
    } catch (err: any) {
      console.error('Erro detalhado ao salvar empreendimento:', err);
      
      let errorMessage = 'Erro desconhecido ao salvar empreendimento';
      
      if (err.response) {
        // Erro da API
        const { status, data } = err.response;
        console.error('Status da API:', status, 'Dados:', data);
        
        if (status === 400) {
          if (data && typeof data === 'object') {
            // Erros de validação específicos
            const fieldErrors: string[] = [];
            Object.entries(data).forEach(([field, messages]) => {
              if (Array.isArray(messages)) {
                fieldErrors.push(`${field}: ${messages.join(', ')}`);
              } else if (typeof messages === 'string') {
                fieldErrors.push(`${field}: ${messages}`);
              }
            });
            errorMessage = `Erros de validação:\n${fieldErrors.join('\n')}`;
          } else if (typeof data === 'string') {
            errorMessage = data;
          }
        } else if (status === 401) {
          errorMessage = 'Sessão expirada. Faça login novamente.';
        } else if (status === 403) {
          errorMessage = 'Sem permissão para realizar esta ação.';
        } else if (status === 404) {
          errorMessage = 'Recurso não encontrado.';
        } else if (status === 500) {
          errorMessage = 'Erro interno do servidor. Tente novamente mais tarde.';
        } else {
          errorMessage = `Erro do servidor (${status}): ${data || 'Erro desconhecido'}`;
        }
      } else if (err.request) {
        // Erro de rede
        errorMessage = 'Erro de conexão. Verifique sua internet e tente novamente.';
      } else if (err.message) {
        // Erro JavaScript
        errorMessage = err.message;
      }
      
      setError(errorMessage);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-[#29306a]">Novo Empreendimento</h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Cadastre um novo empreendimento no sistema
          </p>
          <div className="mt-4 flex items-center space-x-4">
            <div className={`flex items-center ${step >= 1 ? 'text-[#32bef0]' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${step >= 1 ? 'border-[#32bef0] bg-[#32bef0] text-white' : 'border-gray-300'}`}>
                1
              </div>
              <span className="ml-2 text-sm font-medium">Informações Básicas</span>
            </div>
            <div className="flex-1 h-px bg-gray-300"></div>
            <div className={`flex items-center ${step >= 2 ? 'text-[#32bef0]' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${step >= 2 ? 'border-[#32bef0] bg-[#32bef0] text-white' : 'border-gray-300'}`}>
                2
              </div>
              <span className="ml-2 text-sm font-medium">Informações Específicas</span>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
            <div className="flex justify-between items-start">
              <div className="flex-1 whitespace-pre-line">
                {error}
              </div>
              <button
                onClick={() => setError(null)}
                className="ml-4 text-red-400 hover:text-red-600"
              >
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {step === 1 ? (
          <EmpreendimentoFormBasico 
            onNext={handleBasicSubmit} 
            initialData={basicData || undefined}
          />
        ) : (
          <EmpreendimentoFormEspecifico 
            basicData={basicData!}
            onSave={handleSpecificSubmit}
            onBack={handleBack}
            initialSpecificData={specificData}
          />
        )}
      </div>
    </div>
  );
};

export default NovoEmpreendimento; 