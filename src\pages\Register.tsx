import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import { authService } from "../services/auth";
import { UserRegister } from "../types/auth";
import "../styles/colors.css";

export const Register: React.FC = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<UserRegister & { password_confirm: string }>();

  const onSubmit = async (
    data: UserRegister & { password_confirm: string } 
  ) => {
    data.tipo = 'FRQ'
    
    setError(null);
    setLoading(true);

    try {
      const { password_confirm, ...registerData } = data;
      registerData.codigovinculacao = registerData.codigovinculacao.replace(/\s+/g, "");
      await authService.register(registerData);
      navigate("/login", {
        replace: true,
        state: {
          message: "Cadastro realizado com sucesso! Faça login para continuar.",
        },
      });
    } catch (err) {
      console.error("Erro ao registrar:", err);
      setError("Erro ao criar conta. Por favor, tente novamente.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#29306a] to-[#32bef0] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-xl">
        <div>
          <img
            src="/TACTGEO_Logo.png"
            alt="Logo Tact IMOBI"
            className="mx-auto h-16 w-auto mb-2"
            loading="lazy"
            draggable={false}
          />
          <h2 className="mt-6 text-center text-3xl font-extrabold text-[#29306a]">
            Cadastre-se como franqueado
          </h2>
          <p className="mt-2 text-center text-sm text-[#64748b]">
            Preencha os dados abaixo para criar sua conta
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div className="mb-4">
              <label
                htmlFor="nome_completo"
                className="block text-sm font-medium text-gray-700 mb-1 mt-4"
              >
                Nome Completo
              </label>
              <input
                id="nome_completo"
                type="text"
                {...register("nome_completo", {
                  required: "Nome completo é obrigatório",
                  minLength: {
                    value: 3,
                    message: "Nome deve ter pelo menos 3 caracteres",
                  },
                })}
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#32bef0] focus:border-[#32bef0] focus:z-10 sm:text-sm"
                placeholder="Seu nome completo"
              />
              {errors.nome_completo && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.nome_completo.message}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1 mt-4"
              >
                Email
              </label>
              <input
                id="email"
                type="email"
                {...register("email", {
                  required: "Email é obrigatório",
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "Email inválido",
                  },
                })}
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#32bef0] focus:border-[#32bef0] focus:z-10 sm:text-sm"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1 mt-4"
              >
                Senha
              </label>
              <input
                id="password"
                type="password"
                {...register("password", {
                  required: "Senha é obrigatória",
                  minLength: {
                    value: 6,
                    message: "A senha deve ter pelo menos 6 caracteres",
                  },
                })}
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#32bef0] focus:border-[#32bef0] focus:z-10 sm:text-sm"
                placeholder="••••••••"
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.password.message}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label
                htmlFor="password_confirm"
                className="block text-sm font-medium text-gray-700 mb-1 mt-4"
              >
                Confirme a Senha
              </label>
              <input
                id="password_confirm"
                type="password"
                {...register("password_confirm", {
                  required: "Confirmação de senha é obrigatória",
                  validate: (value) =>
                    value === watch("password") || "As senhas não coincidem",
                })}
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#32bef0] focus:border-[#32bef0] focus:z-10 sm:text-sm"
                placeholder="••••••••"
              />
              {errors.password_confirm && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.password_confirm.message}
                </p>
              )}
            </div>
          </div>

          <div className="rounded-md shadow-sm -space-y-px">
            <div className="mb-4">
              <label
                htmlFor="codigovinculacao"
                className="block text-sm font-medium text-gray-700 mb-1 mt-4"
              >
                Código do Vinculador
              </label>
              <input
                id="codigovinculacao"
                type="text"
                {...register("codigovinculacao", {
                  required: "Código do Vinculador é obrigatório",
                  minLength: {
                    value: 10,
                    message: "Código deve ter pelo menos 10 caracteres",
                  },
                })}
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#32bef0] focus:border-[#32bef0] focus:z-10 sm:text-sm"
                placeholder="ex: f47ac10b-58cc-4372-a567-0e02b2c3d479"
              />
              {errors.codigovinculacao && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.codigovinculacao.message}
                </p>
              )}
          </div>
        </div>
          
          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">{error}</h3>
                </div>
              </div>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white ${
                loading
                  ? "bg-[#32bef0] opacity-50 cursor-not-allowed"
                  : "bg-[#32bef0] hover:bg-[#2ba8d8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0]"
              }`}
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
              ) : (
                "Cadastrar"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
