openapi: 3.0.3
info:
  title: Tact IMOBI API
  version: 0.1.0
  description: Transformando dados em estratégias de negócio
paths:
  /api/areas-cidade/:
    get:
      operationId: areas_cidade_list
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - areas-cidade
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAreaCidadeList'
          description: ''
    post:
      operationId: areas_cidade_create
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      tags:
      - areas-cidade
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AreaCidade'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AreaCidade'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AreaCidade'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaCidade'
          description: ''
  /api/areas-cidade/{id}/:
    get:
      operationId: areas_cidade_retrieve
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this area cidade.
        required: true
      tags:
      - areas-cidade
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaCidade'
          description: ''
    put:
      operationId: areas_cidade_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this area cidade.
        required: true
      tags:
      - areas-cidade
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AreaCidade'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AreaCidade'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AreaCidade'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaCidade'
          description: ''
    patch:
      operationId: areas_cidade_partial_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this area cidade.
        required: true
      tags:
      - areas-cidade
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAreaCidade'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAreaCidade'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAreaCidade'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaCidade'
          description: ''
    delete:
      operationId: areas_cidade_destroy
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this area cidade.
        required: true
      tags:
      - areas-cidade
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/assinantes/:
    get:
      operationId: assinantes_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - assinantes
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAssinanteList'
          description: ''
    post:
      operationId: assinantes_create
      tags:
      - assinantes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Assinante'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Assinante'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Assinante'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assinante'
          description: ''
  /api/assinantes/{id}/:
    get:
      operationId: assinantes_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this assinante.
        required: true
      tags:
      - assinantes
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assinante'
          description: ''
    put:
      operationId: assinantes_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this assinante.
        required: true
      tags:
      - assinantes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Assinante'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Assinante'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Assinante'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assinante'
          description: ''
    patch:
      operationId: assinantes_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this assinante.
        required: true
      tags:
      - assinantes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAssinante'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAssinante'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAssinante'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assinante'
          description: ''
    delete:
      operationId: assinantes_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this assinante.
        required: true
      tags:
      - assinantes
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/assinaturas/:
    get:
      operationId: assinaturas_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - assinaturas
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAssinaturaList'
          description: ''
    post:
      operationId: assinaturas_create
      tags:
      - assinaturas
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Assinatura'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Assinatura'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Assinatura'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assinatura'
          description: ''
  /api/assinaturas/{id}/:
    get:
      operationId: assinaturas_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this assinatura.
        required: true
      tags:
      - assinaturas
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assinatura'
          description: ''
    put:
      operationId: assinaturas_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this assinatura.
        required: true
      tags:
      - assinaturas
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Assinatura'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Assinatura'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Assinatura'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assinatura'
          description: ''
    patch:
      operationId: assinaturas_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this assinatura.
        required: true
      tags:
      - assinaturas
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAssinatura'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAssinatura'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAssinatura'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assinatura'
          description: ''
    delete:
      operationId: assinaturas_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this assinatura.
        required: true
      tags:
      - assinaturas
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/auth/change-password/:
    post:
      operationId: auth_change_password_create
      description: Change the user password.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePassword'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChangePassword'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChangePassword'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChangePassword'
          description: ''
  /api/auth/login/:
    post:
      operationId: auth_login_create
      description: Logs in the user via given login and password.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLogin'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserLogin'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserLogin'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserLogin'
          description: ''
  /api/auth/logout/:
    post:
      operationId: auth_logout_create
      description: |-
        Logs out the user. returns an error if the user is not
        authenticated.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Logout'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Logout'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Logout'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Logout'
          description: ''
  /api/auth/profile/:
    get:
      operationId: auth_profile_retrieve
      tags:
      - auth
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
    post:
      operationId: auth_profile_create
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfile'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserProfile'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserProfile'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
    put:
      operationId: auth_profile_update
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfile'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserProfile'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserProfile'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
    patch:
      operationId: auth_profile_partial_update
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserProfile'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserProfile'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserProfile'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
  /api/auth/register/:
    post:
      operationId: auth_register_create
      description: Register new user.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegister'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRegister'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRegister'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRegister'
          description: ''
  /api/auth/register-email/:
    post:
      operationId: auth_register_email_create
      description: Register new email.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DefaultRegisterEmail'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DefaultRegisterEmail'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DefaultRegisterEmail'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DefaultRegisterEmail'
          description: ''
  /api/auth/reset-password/:
    post:
      operationId: auth_reset_password_create
      description: Reset password, given the signature and timestamp from the link.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPassword'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ResetPassword'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ResetPassword'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResetPassword'
          description: ''
  /api/auth/send-reset-password-link/:
    post:
      operationId: auth_send_reset_password_link_create
      description: Send email with reset password link.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserResetPasswordLink'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserResetPasswordLink'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserResetPasswordLink'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResetPasswordLink'
          description: ''
  /api/auth/verify-email/:
    post:
      operationId: auth_verify_email_create
      description: Verify email via signature.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifyEmail'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/VerifyEmail'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/VerifyEmail'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerifyEmail'
          description: ''
  /api/auth/verify-registration/:
    post:
      operationId: auth_verify_registration_create
      description: Verify registration via signature.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifyRegistration'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/VerifyRegistration'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/VerifyRegistration'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerifyRegistration'
          description: ''
  /api/cidades/:
    get:
      operationId: cidades_list
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - cidades
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCidadeList'
          description: ''
    post:
      operationId: cidades_create
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      tags:
      - cidades
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Cidade'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Cidade'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Cidade'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cidade'
          description: ''
  /api/cidades/{id}/:
    get:
      operationId: cidades_retrieve
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this Cidade.
        required: true
      tags:
      - cidades
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cidade'
          description: ''
    put:
      operationId: cidades_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this Cidade.
        required: true
      tags:
      - cidades
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Cidade'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Cidade'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Cidade'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cidade'
          description: ''
    patch:
      operationId: cidades_partial_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this Cidade.
        required: true
      tags:
      - cidades
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCidade'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCidade'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCidade'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cidade'
          description: ''
    delete:
      operationId: cidades_destroy
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this Cidade.
        required: true
      tags:
      - cidades
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/empreendimentos/:
    get:
      operationId: empreendimentos_list
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: query
        name: cidade
        schema:
          type: integer
        description: ID da cidade para filtrar
      - in: query
        name: incorporadora
        schema:
          type: integer
        description: ID da incorporadora para filtrar
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      - in: query
        name: search
        schema:
          type: string
        description: Filtrar empreendimentos por nome (busca parcial)
      tags:
      - empreendimentos
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedEmpreendimentoList'
          description: ''
    post:
      operationId: empreendimentos_create
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      tags:
      - empreendimentos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
          description: ''
  /api/empreendimentos/{id}/:
    get:
      operationId: empreendimentos_retrieve
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this empreendimento.
        required: true
      tags:
      - empreendimentos
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Empreendimento'
          description: ''
    put:
      operationId: empreendimentos_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this empreendimento.
        required: true
      tags:
      - empreendimentos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
          description: ''
    patch:
      operationId: empreendimentos_partial_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this empreendimento.
        required: true
      tags:
      - empreendimentos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmpreendimentoCreateUpdate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmpreendimentoCreateUpdate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmpreendimentoCreateUpdate'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmpreendimentoCreateUpdate'
          description: ''
    delete:
      operationId: empreendimentos_destroy
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this empreendimento.
        required: true
      tags:
      - empreendimentos
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/empresas/:
    get:
      operationId: empresas_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - empresas
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedEmpresaList'
          description: ''
    post:
      operationId: empresas_create
      tags:
      - empresas
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Empresa'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Empresa'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Empresa'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Empresa'
          description: ''
  /api/empresas/{id}/:
    get:
      operationId: empresas_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this empresa.
        required: true
      tags:
      - empresas
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Empresa'
          description: ''
    put:
      operationId: empresas_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this empresa.
        required: true
      tags:
      - empresas
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Empresa'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Empresa'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Empresa'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Empresa'
          description: ''
    patch:
      operationId: empresas_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this empresa.
        required: true
      tags:
      - empresas
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmpresa'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmpresa'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmpresa'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Empresa'
          description: ''
    delete:
      operationId: empresas_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this empresa.
        required: true
      tags:
      - empresas
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/enderecos/:
    get:
      operationId: enderecos_list
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - enderecos
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedEnderecoList'
          description: ''
    post:
      operationId: enderecos_create
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      tags:
      - enderecos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Endereco'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Endereco'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Endereco'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Endereco'
          description: ''
  /api/enderecos/{id}/:
    get:
      operationId: enderecos_retrieve
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this endereco.
        required: true
      tags:
      - enderecos
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Endereco'
          description: ''
    put:
      operationId: enderecos_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this endereco.
        required: true
      tags:
      - enderecos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Endereco'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Endereco'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Endereco'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Endereco'
          description: ''
    patch:
      operationId: enderecos_partial_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this endereco.
        required: true
      tags:
      - enderecos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEndereco'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEndereco'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEndereco'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Endereco'
          description: ''
    delete:
      operationId: enderecos_destroy
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this endereco.
        required: true
      tags:
      - enderecos
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/franqueados/:
    get:
      operationId: franqueados_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - franqueados
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFranqueadoList'
          description: ''
    post:
      operationId: franqueados_create
      tags:
      - franqueados
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Franqueado'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Franqueado'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Franqueado'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Franqueado'
          description: ''
  /api/franqueados/{id}/:
    get:
      operationId: franqueados_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this franqueado.
        required: true
      tags:
      - franqueados
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Franqueado'
          description: ''
    put:
      operationId: franqueados_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this franqueado.
        required: true
      tags:
      - franqueados
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Franqueado'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Franqueado'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Franqueado'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Franqueado'
          description: ''
    patch:
      operationId: franqueados_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this franqueado.
        required: true
      tags:
      - franqueados
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedFranqueado'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedFranqueado'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedFranqueado'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Franqueado'
          description: ''
    delete:
      operationId: franqueados_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this franqueado.
        required: true
      tags:
      - franqueados
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/historico-fase-empreendimento/:
    get:
      operationId: historico_fase_empreendimento_list
      parameters:
      - in: query
        name: empreendimento
        schema:
          type: integer
        description: ID do empreendimento (obrigatório)
        required: true
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - historico-fase-empreendimento
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedHistoricoFaseEmpreendimentoList'
          description: ''
  /api/historico-fase-empreendimento/{id}/:
    get:
      operationId: historico_fase_empreendimento_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this historico fase empreendimento.
        required: true
      tags:
      - historico-fase-empreendimento
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HistoricoFaseEmpreendimento'
          description: ''
  /api/incorporadoras/:
    get:
      operationId: incorporadoras_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - incorporadoras
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedIncorporadoraList'
          description: ''
    post:
      operationId: incorporadoras_create
      tags:
      - incorporadoras
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Incorporadora'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Incorporadora'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Incorporadora'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Incorporadora'
          description: ''
  /api/incorporadoras/{id}/:
    get:
      operationId: incorporadoras_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this incorporadora.
        required: true
      tags:
      - incorporadoras
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Incorporadora'
          description: ''
    put:
      operationId: incorporadoras_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this incorporadora.
        required: true
      tags:
      - incorporadoras
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Incorporadora'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Incorporadora'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Incorporadora'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Incorporadora'
          description: ''
    patch:
      operationId: incorporadoras_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this incorporadora.
        required: true
      tags:
      - incorporadoras
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedIncorporadora'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedIncorporadora'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedIncorporadora'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Incorporadora'
          description: ''
    delete:
      operationId: incorporadoras_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this incorporadora.
        required: true
      tags:
      - incorporadoras
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/ivv/:
    get:
      operationId: ivv_list
      parameters:
      - in: query
        name: ano
        schema:
          type: integer
        description: Ano da movimentação
        required: true
      - in: query
        name: empreendimento
        schema:
          type: integer
        description: ID do empreendimento
        required: true
      - in: query
        name: mes
        schema:
          type: integer
        description: Mês da movimentação (1-12)
        required: true
      tags:
      - ivv
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/IVV'
          description: ''
  /api/logradouros/:
    get:
      operationId: logradouros_list
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - logradouros
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedLogradouroList'
          description: ''
    post:
      operationId: logradouros_create
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      tags:
      - logradouros
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Logradouro'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Logradouro'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Logradouro'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Logradouro'
          description: ''
  /api/logradouros/{id}/:
    get:
      operationId: logradouros_retrieve
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this logradouro.
        required: true
      tags:
      - logradouros
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Logradouro'
          description: ''
    put:
      operationId: logradouros_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this logradouro.
        required: true
      tags:
      - logradouros
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Logradouro'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Logradouro'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Logradouro'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Logradouro'
          description: ''
    patch:
      operationId: logradouros_partial_update
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this logradouro.
        required: true
      tags:
      - logradouros
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedLogradouro'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedLogradouro'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedLogradouro'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Logradouro'
          description: ''
    delete:
      operationId: logradouros_destroy
      description: |-
        Mixin para padronizar respostas de erro em toda a API.
        Garante que todos os erros tenham o mesmo formato.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this logradouro.
        required: true
      tags:
      - logradouros
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/logradouros/by_zipcode/:
    get:
      operationId: logradouros_by_zipcode_list
      description: Busca endereços por CEP. Retorna lista vazia se CEP não existir.
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      - in: query
        name: zipcode
        schema:
          type: string
        description: CEP com 8 dígitos numéricos
        required: true
      tags:
      - logradouros
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedEnderecoList'
          description: ''
  /api/movimentacoes/:
    get:
      operationId: movimentacoes_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - movimentacoes
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedMovimentacaoList'
          description: ''
    post:
      operationId: movimentacoes_create
      tags:
      - movimentacoes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Movimentacao'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Movimentacao'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Movimentacao'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Movimentacao'
          description: ''
  /api/movimentacoes/{id}/:
    get:
      operationId: movimentacoes_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this movimentacao.
        required: true
      tags:
      - movimentacoes
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Movimentacao'
          description: ''
  /api/paises/:
    get:
      operationId: paises_list
      description: Lista todos os países disponíveis
      tags:
      - paises
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Pais'
          description: ''
  /api/parte-empreendimento/:
    get:
      operationId: parte_empreendimento_list
      parameters:
      - in: query
        name: empreendimento
        schema:
          type: integer
        description: ID do empreendimento (obrigatório)
        required: true
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - parte-empreendimento
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedParteEmpreendimentoList'
          description: ''
    post:
      operationId: parte_empreendimento_create
      tags:
      - parte-empreendimento
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParteEmpreendimento'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ParteEmpreendimento'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ParteEmpreendimento'
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParteEmpreendimento'
          description: ''
  /api/parte-empreendimento/{id}/:
    get:
      operationId: parte_empreendimento_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this parte empreendimento.
        required: true
      tags:
      - parte-empreendimento
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParteEmpreendimento'
          description: ''
    put:
      operationId: parte_empreendimento_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this parte empreendimento.
        required: true
      tags:
      - parte-empreendimento
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParteEmpreendimento'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ParteEmpreendimento'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ParteEmpreendimento'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParteEmpreendimento'
          description: ''
    patch:
      operationId: parte_empreendimento_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this parte empreendimento.
        required: true
      tags:
      - parte-empreendimento
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedParteEmpreendimento'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedParteEmpreendimento'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedParteEmpreendimento'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParteEmpreendimento'
          description: ''
    delete:
      operationId: parte_empreendimento_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this parte empreendimento.
        required: true
      tags:
      - parte-empreendimento
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/planos/:
    get:
      operationId: planos_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - planos
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPlanoList'
          description: ''
    post:
      operationId: planos_create
      tags:
      - planos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Plano'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Plano'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Plano'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Plano'
          description: ''
  /api/planos/{id}/:
    get:
      operationId: planos_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this plano.
        required: true
      tags:
      - planos
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Plano'
          description: ''
    put:
      operationId: planos_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this plano.
        required: true
      tags:
      - planos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Plano'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Plano'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Plano'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Plano'
          description: ''
    patch:
      operationId: planos_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this plano.
        required: true
      tags:
      - planos
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPlano'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPlano'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPlano'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Plano'
          description: ''
    delete:
      operationId: planos_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this plano.
        required: true
      tags:
      - planos
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/rest/rest-check/:
    get:
      operationId: rest_rest_check_retrieve
      description: This endpoint checks if the REST API is working.
      summary: Check REST API
      tags:
      - rest
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
              examples:
                SuccessfulResponse:
                  value:
                    message: This message comes from the backend. If you're seeing
                      this, the REST API is working!
                  summary: Successful Response
          description: ''
  /api/telefones/:
    get:
      operationId: telefones_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - telefones
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTelefoneList'
          description: ''
    post:
      operationId: telefones_create
      tags:
      - telefones
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Telefone'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Telefone'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Telefone'
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telefone'
          description: ''
  /api/telefones/{id}/:
    get:
      operationId: telefones_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - telefones
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telefone'
          description: ''
    put:
      operationId: telefones_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - telefones
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Telefone'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Telefone'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Telefone'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telefone'
          description: ''
    patch:
      operationId: telefones_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - telefones
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedTelefone'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedTelefone'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedTelefone'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telefone'
          description: ''
    delete:
      operationId: telefones_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - telefones
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/telefones/{id}/delete_my_telefone/:
    delete:
      operationId: telefones_delete_my_telefone_destroy
      description: Endpoint para deletar um telefone específico do usuário logado
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - telefones
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/telefones/my_telefones/:
    get:
      operationId: telefones_my_telefones_retrieve
      description: Endpoint para listar telefones do usuário logado
      tags:
      - telefones
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telefone'
          description: ''
  /api/ufs/:
    get:
      operationId: ufs_list
      description: Lista todas as UFs do país selecionado
      parameters:
      - in: query
        name: pais
        schema:
          type: string
        description: 'Código do país (ex: BR)'
      tags:
      - ufs
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UFsBrasil'
          description: ''
  /api/users/:
    get:
      operationId: users_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: users_create
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/users/{id}/:
    get:
      operationId: users_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: users_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    patch:
      operationId: users_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUser'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    delete:
      operationId: users_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
components:
  schemas:
    AnoDisponibilidadeEnum:
      enum:
      - 2000
      - 2001
      - 2002
      - 2003
      - 2004
      - 2005
      - 2006
      - 2007
      - 2008
      - 2009
      - 2010
      - 2011
      - 2012
      - 2013
      - 2014
      - 2015
      - 2016
      - 2017
      - 2018
      - 2019
      - 2020
      - 2021
      - 2022
      - 2023
      - 2024
      - 2025
      - 2026
      - 2027
      - 2028
      - 2029
      - 2030
      - 2031
      - 2032
      - 2033
      - 2034
      type: integer
      description: |-
        * `2000` - 2000
        * `2001` - 2001
        * `2002` - 2002
        * `2003` - 2003
        * `2004` - 2004
        * `2005` - 2005
        * `2006` - 2006
        * `2007` - 2007
        * `2008` - 2008
        * `2009` - 2009
        * `2010` - 2010
        * `2011` - 2011
        * `2012` - 2012
        * `2013` - 2013
        * `2014` - 2014
        * `2015` - 2015
        * `2016` - 2016
        * `2017` - 2017
        * `2018` - 2018
        * `2019` - 2019
        * `2020` - 2020
        * `2021` - 2021
        * `2022` - 2022
        * `2023` - 2023
        * `2024` - 2024
        * `2025` - 2025
        * `2026` - 2026
        * `2027` - 2027
        * `2028` - 2028
        * `2029` - 2029
        * `2030` - 2030
        * `2031` - 2031
        * `2032` - 2032
        * `2033` - 2033
        * `2034` - 2034
    AreaCidade:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        nome:
          type: string
          maxLength: 255
        cidade:
          type: integer
        franqueado:
          allOf:
          - $ref: '#/components/schemas/Franqueado'
          readOnly: true
        franqueado_id:
          type: integer
          writeOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
      required:
      - cidade
      - created
      - franqueado
      - franqueado_id
      - id
      - modified
      - nome
    Assinante:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        nome:
          type: string
          maxLength: 255
        email:
          type: string
          format: email
          maxLength: 254
        usuario:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        usuario_id:
          type: integer
          writeOnly: true
        cidades:
          type: array
          items:
            type: integer
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
      required:
      - cidades
      - created
      - email
      - id
      - modified
      - nome
      - usuario
      - usuario_id
    Assinatura:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        data_assinatura:
          type: string
          format: date
          readOnly: true
        recorrencia:
          $ref: '#/components/schemas/RecorrenciaEnum'
        data_expiracao:
          type: string
          format: date
          nullable: true
        forma_pagamento:
          $ref: '#/components/schemas/FormaPagamentoEnum'
        assinante:
          type: integer
        cidade:
          type: integer
        plano:
          type: integer
      required:
      - assinante
      - cidade
      - created
      - data_assinatura
      - forma_pagamento
      - id
      - modified
      - plano
    BlankEnum:
      enum:
      - ''
    ChangePassword:
      type: object
      properties:
        old_password:
          type: string
        password:
          type: string
        password_confirm:
          type: string
          writeOnly: true
      required:
      - old_password
      - password
      - password_confirm
    Cidade:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome:
          type: string
          maxLength: 100
        pais:
          $ref: '#/components/schemas/PaisEnum'
        uf:
          $ref: '#/components/schemas/UfEnum'
        estado:
          type: string
          maxLength: 100
      required:
      - created
      - estado
      - id
      - modified
      - nome
      - uf
    DefaultRegisterEmail:
      type: object
      description: Default serializer used for e-mail registration (e-mail change).
      properties:
        email:
          type: string
          format: email
      required:
      - email
    Empreendimento:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        endereco:
          type: integer
        fase_empreendimento:
          type: integer
          maximum: 5
          minimum: 1
          writeOnly: true
        tipo_empreendimento:
          type: string
          writeOnly: true
          maxLength: 45
        item_empreendimento:
          type: array
          items:
            type: string
            maxLength: 45
          writeOnly: true
        fase_atual:
          type: integer
          readOnly: true
        tipo_empreendimento_descricao:
          type: string
          readOnly: true
        itens_empreendimento_descricoes:
          type: array
          items:
            type: string
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome:
          type: string
          maxLength: 150
        quantidade_garagens:
          type: integer
          maximum: 2147483647
          minimum: 0
        data_entrega:
          type: string
          format: date
          nullable: true
        data_inicio_comercializacao:
          type: string
          format: date
          nullable: true
        observacao:
          type: string
        heading:
          type: string
          maxLength: 254
        pitch:
          type: string
          maxLength: 254
        incorporadora:
          type: integer
      required:
      - created
      - endereco
      - fase_atual
      - heading
      - id
      - incorporadora
      - itens_empreendimento_descricoes
      - modified
      - nome
      - pitch
      - tipo_empreendimento_descricao
    EmpreendimentoCreateUpdate:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        endereco:
          type: integer
        fase_empreendimento:
          type: integer
          maximum: 5
          minimum: 1
          writeOnly: true
        tipo_empreendimento:
          type: string
          writeOnly: true
          maxLength: 45
        item_empreendimento:
          type: array
          items:
            type: string
            maxLength: 45
          writeOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome:
          type: string
          maxLength: 150
        quantidade_garagens:
          type: integer
          maximum: 2147483647
          minimum: 0
        data_entrega:
          type: string
          format: date
          nullable: true
        data_inicio_comercializacao:
          type: string
          format: date
          nullable: true
        observacao:
          type: string
        heading:
          type: string
          maxLength: 254
        pitch:
          type: string
          maxLength: 254
        incorporadora:
          type: integer
      required:
      - created
      - endereco
      - heading
      - id
      - incorporadora
      - modified
      - nome
      - pitch
    Empresa:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        endereco:
          type: integer
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        razao_social:
          type: string
          maxLength: 254
        nome_fantasia:
          type: string
          maxLength: 254
        cnpj:
          type: string
          maxLength: 18
      required:
      - cnpj
      - created
      - id
      - modified
      - nome_fantasia
      - razao_social
    Endereco:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        logradouro:
          type: integer
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        numero:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        complemento:
          type: string
          nullable: true
          maxLength: 100
        ponto_referencia:
          type: string
          nullable: true
          maxLength: 100
        latitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,6})?$
        longitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,6})?$
      required:
      - created
      - id
      - logradouro
      - modified
    FaseEmpreendimentoEnum:
      enum:
      - 1
      - 2
      - 3
      - 4
      - 5
      type: integer
      description: |-
        * `1` - Planta
        * `2` - Fundação
        * `3` - Estrutura
        * `4` - Acabamento
        * `5` - Pronto
    FormaPagamentoEnum:
      enum:
      - cartao
      - boleto
      - pix
      - debito
      type: string
      description: |-
        * `cartao` - Cartão de Crédito
        * `boleto` - Boleto
        * `pix` - PIX
        * `debito` - Débito em Conta
    Franqueado:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        usuarios:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome:
          type: string
          maxLength: 255
        email:
          type: string
          format: email
          maxLength: 254
      required:
      - created
      - email
      - id
      - modified
      - nome
      - usuarios
    GeneroEnum:
      enum:
      - MASCULINO
      - FEMININO
      - OUTROS
      type: string
      description: |-
        * `MASCULINO` - Masculino
        * `FEMININO` - Feminino
        * `OUTROS` - Prefiro não declarar
    HistoricoFaseEmpreendimento:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        fase_empreendimento:
          type: integer
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        empreendimento:
          type: integer
          readOnly: true
      required:
      - created
      - empreendimento
      - fase_empreendimento
      - id
      - modified
    IVV:
      type: object
      properties:
        ivv:
          type: number
          format: double
          description: Índice de Velocidade de Vendas (IVV)
      required:
      - ivv
    Incorporadora:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        empresa:
          type: integer
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
      required:
      - created
      - empresa
      - id
      - modified
    Logout:
      type: object
      properties:
        revoke_token:
          type: boolean
          default: false
    Logradouro:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        cidade:
          type: integer
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        zipcode:
          type: string
          maxLength: 10
        nome:
          type: string
          maxLength: 100
        bairro:
          type: string
          maxLength: 45
      required:
      - bairro
      - cidade
      - created
      - id
      - modified
      - nome
      - zipcode
    MesDisponibilidadeEnum:
      enum:
      - 1
      - 2
      - 3
      - 4
      - 5
      - 6
      - 7
      - 8
      - 9
      - 10
      - 11
      - 12
      type: integer
      description: |-
        * `1` - 01
        * `2` - 02
        * `3` - 03
        * `4` - 04
        * `5` - 05
        * `6` - 06
        * `7` - 07
        * `8` - 08
        * `9` - 09
        * `10` - 10
        * `11` - 11
        * `12` - 12
    MesMovimentacaoEnum:
      enum:
      - 1
      - 2
      - 3
      - 4
      - 5
      - 6
      - 7
      - 8
      - 9
      - 10
      - 11
      - 12
      type: integer
      description: |-
        * `1` - 01
        * `2` - 02
        * `3` - 03
        * `4` - 04
        * `5` - 05
        * `6` - 06
        * `7` - 07
        * `8` - 08
        * `9` - 09
        * `10` - 10
        * `11` - 11
        * `12` - 12
    Message:
      type: object
      properties:
        message:
          type: string
      required:
      - message
    MotivoDistratoEnum:
      enum:
      - 1
      - 2
      - 3
      - 4
      - 5
      - 6
      - 7
      - 8
      - 9
      type: integer
      description: |-
        * `1` - Troca de unidade
        * `2` - Negativa de crédito
        * `3` - Troca de empreendimento
        * `4` - Diminuição de Renda
        * `5` - Transferência de cidade
        * `6` - Problema Familiar
        * `7` - Perda de Interesse
        * `8` - Desemprego
        * `9` - Outros
    Movimentacao:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        incorporadora:
          allOf:
          - $ref: '#/components/schemas/Incorporadora'
          readOnly: true
        empreendimento:
          allOf:
          - $ref: '#/components/schemas/Empreendimento'
          readOnly: true
        tipo_movimentacao:
          $ref: '#/components/schemas/TipoMovimentacaoEnum'
        quantidade:
          type: integer
          maximum: 2147483647
          minimum: 1
          nullable: true
        ano_movimentacao:
          type: integer
          maximum: 2025
          minimum: 2000
          nullable: true
          title: Ano da Movimentação
        mes_movimentacao:
          nullable: true
          title: Mês da Movimentação
          minimum: 1
          maximum: 12
          oneOf:
          - $ref: '#/components/schemas/MesMovimentacaoEnum'
          - $ref: '#/components/schemas/NullEnum'
        endereco:
          allOf:
          - $ref: '#/components/schemas/Endereco'
          readOnly: true
        padrao_acabamento:
          allOf:
          - $ref: '#/components/schemas/PadraoAcabamentoEnum'
          readOnly: true
        parte_empreendimento:
          allOf:
          - $ref: '#/components/schemas/ParteEmpreendimento'
          readOnly: true
        valor_m2:
          type: string
          format: decimal
          pattern: ^-?\d{0,12}(?:\.\d{0,2})?$
          readOnly: true
          nullable: true
          title: Valor por m²
        fase_empreendimento:
          allOf:
          - $ref: '#/components/schemas/FaseEmpreendimentoEnum'
          readOnly: true
        origem_recurso:
          $ref: '#/components/schemas/OrigemRecursoEnum'
        motivo_distrato:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/MotivoDistratoEnum'
          - $ref: '#/components/schemas/NullEnum'
        was_lancamento:
          type: boolean
          description: Indica se esta movimentação é um lançamento
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
      required:
      - created
      - empreendimento
      - endereco
      - fase_empreendimento
      - id
      - incorporadora
      - modified
      - motivo_distrato
      - origem_recurso
      - padrao_acabamento
      - parte_empreendimento
      - tipo_movimentacao
      - valor_m2
    NullEnum:
      enum:
      - null
    OrigemRecursoEnum:
      enum:
      - 1
      - 2
      - 3
      - 4
      - 5
      - 6
      - 7
      type: integer
      description: |-
        * `1` - Próprio
        * `2` - SFH
        * `3` - Condomínio
        * `4` - Cooperativa
        * `5` - Próprio+SFH
        * `6` - OUTROS
        * `7` - MCMV
    PadraoAcabamentoEnum:
      enum:
      - 1
      - 2
      - 3
      type: integer
      description: |-
        * `1` - Alto
        * `2` - Medio
        * `3` - Baixo
    PaginatedAreaCidadeList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/AreaCidade'
    PaginatedAssinanteList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Assinante'
    PaginatedAssinaturaList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Assinatura'
    PaginatedCidadeList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Cidade'
    PaginatedEmpreendimentoList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Empreendimento'
    PaginatedEmpresaList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Empresa'
    PaginatedEnderecoList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Endereco'
    PaginatedFranqueadoList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Franqueado'
    PaginatedHistoricoFaseEmpreendimentoList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/HistoricoFaseEmpreendimento'
    PaginatedIncorporadoraList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Incorporadora'
    PaginatedLogradouroList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Logradouro'
    PaginatedMovimentacaoList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Movimentacao'
    PaginatedParteEmpreendimentoList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/ParteEmpreendimento'
    PaginatedPlanoList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Plano'
    PaginatedTelefoneList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/Telefone'
    PaginatedUserList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/User'
    Pais:
      type: object
      properties:
        value:
          type: string
        label:
          type: string
      required:
      - label
      - value
    PaisEnum:
      enum:
      - BR
      type: string
      description: '* `BR` - Brasil'
    ParteEmpreendimento:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        estoque_inicial:
          type: integer
          writeOnly: true
          default: 0
          minimum: 0
        was_lancamento:
          type: boolean
          writeOnly: true
          default: false
        ano_disponibilidade:
          allOf:
          - $ref: '#/components/schemas/AnoDisponibilidadeEnum'
          writeOnly: true
        mes_disponibilidade:
          allOf:
          - $ref: '#/components/schemas/MesDisponibilidadeEnum'
          writeOnly: true
        origem_recurso:
          allOf:
          - $ref: '#/components/schemas/OrigemRecursoEnum'
          writeOnly: true
        tipo_parte_empreendimento:
          type: integer
          nullable: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome:
          type: string
          maxLength: 254
        area_privativa_m2:
          type: string
          format: decimal
          pattern: ^-?\d{0,12}(?:\.\d{0,2})?$
        valor_imovel:
          type: string
          format: decimal
          pattern: ^-?\d{0,12}(?:\.\d{0,2})?$
        forma_pagamento:
          type: string
        atributos_extras: {}
        empreendimento:
          type: integer
          readOnly: true
      required:
      - created
      - empreendimento
      - id
      - modified
    PatchedAreaCidade:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        nome:
          type: string
          maxLength: 255
        cidade:
          type: integer
        franqueado:
          allOf:
          - $ref: '#/components/schemas/Franqueado'
          readOnly: true
        franqueado_id:
          type: integer
          writeOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
    PatchedAssinante:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        nome:
          type: string
          maxLength: 255
        email:
          type: string
          format: email
          maxLength: 254
        usuario:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        usuario_id:
          type: integer
          writeOnly: true
        cidades:
          type: array
          items:
            type: integer
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
    PatchedAssinatura:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        data_assinatura:
          type: string
          format: date
          readOnly: true
        recorrencia:
          $ref: '#/components/schemas/RecorrenciaEnum'
        data_expiracao:
          type: string
          format: date
          nullable: true
        forma_pagamento:
          $ref: '#/components/schemas/FormaPagamentoEnum'
        assinante:
          type: integer
        cidade:
          type: integer
        plano:
          type: integer
    PatchedCidade:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome:
          type: string
          maxLength: 100
        pais:
          $ref: '#/components/schemas/PaisEnum'
        uf:
          $ref: '#/components/schemas/UfEnum'
        estado:
          type: string
          maxLength: 100
    PatchedEmpreendimentoCreateUpdate:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        endereco:
          type: integer
        fase_empreendimento:
          type: integer
          maximum: 5
          minimum: 1
          writeOnly: true
        tipo_empreendimento:
          type: string
          writeOnly: true
          maxLength: 45
        item_empreendimento:
          type: array
          items:
            type: string
            maxLength: 45
          writeOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome:
          type: string
          maxLength: 150
        quantidade_garagens:
          type: integer
          maximum: 2147483647
          minimum: 0
        data_entrega:
          type: string
          format: date
          nullable: true
        data_inicio_comercializacao:
          type: string
          format: date
          nullable: true
        observacao:
          type: string
        heading:
          type: string
          maxLength: 254
        pitch:
          type: string
          maxLength: 254
        incorporadora:
          type: integer
    PatchedEmpresa:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        endereco:
          type: integer
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        razao_social:
          type: string
          maxLength: 254
        nome_fantasia:
          type: string
          maxLength: 254
        cnpj:
          type: string
          maxLength: 18
    PatchedEndereco:
      type: object
        id:
          type: integer
          readOnly: true
        logradouro:
          type: integer
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        numero:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        complemento:
          type: string
          nullable: true
          maxLength: 100
        ponto_referencia:
          type: string
          nullable: true
          maxLength: 100
        latitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,6})?$
        longitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,6})?$
    PatchedFranqueado:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        usuarios:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome: 
          type: string
          maxLength: 255
        email:
          type: string
          format: email
          maxLength: 254
    PatchedIncorporadora:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        empresa:
          type: integer
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
    PatchedLogradouro:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        cidade:
          type: integer
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        zipcode:
          type: string
          maxLength: 10
        nome:
          type: string
          maxLength: 100
        bairro:
          type: string
          maxLength: 45
    PatchedParteEmpreendimento:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        estoque_inicial:
          type: integer
          writeOnly: true
          default: 0
          minimum: 0
        was_lancamento:
          type: boolean
          writeOnly: true
          default: false
        ano_disponibilidade:
          allOf:
          - $ref: '#/components/schemas/AnoDisponibilidadeEnum'
          writeOnly: true
        mes_disponibilidade:
          allOf:
          - $ref: '#/components/schemas/MesDisponibilidadeEnum'
          writeOnly: true
        origem_recurso:
          allOf:
          - $ref: '#/components/schemas/OrigemRecursoEnum'
          writeOnly: true
        tipo_parte_empreendimento:
          type: integer
          nullable: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        nome:
          type: string
          maxLength: 254
        area_privativa_m2:
          type: string
          format: decimal
          pattern: ^-?\d{0,12}(?:\.\d{0,2})?$
        valor_imovel:
          type: string
          format: decimal
          pattern: ^-?\d{0,12}(?:\.\d{0,2})?$
        forma_pagamento:
          type: string
        atributos_extras: {}
        empreendimento:
          type: integer
          readOnly: true
    PatchedPlano:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        nome:
          type: string
          maxLength: 255
        descricao:
          type: string
        valor:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,2})?$
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
    PatchedTelefone:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        numero:
          type: string
          maxLength: 15
    PatchedUser:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          maxLength: 255
        nome_completo:
          type: string
          maxLength: 255
        cpf:
          type: string
          maxLength: 14
        data_nascimento:
          type: string
          format: date
          nullable: true
        is_active:
          type: boolean
          description: Designates whether this user should be treated as active. Unselect
            this instead of deleting accounts.
        is_staff:
          type: boolean
          description: Designates whether the user can log into this admin site.
        is_superuser:
          type: boolean
          title: Superuser status
          description: Designates that this user has all permissions without explicitly
            assigning them.
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        last_login:
          type: string
          format: date-time
          nullable: true
    PatchedUserProfile:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
        nome_completo:
          type: string
          maxLength: 255
        cpf:
          type: string
          maxLength: 14
        data_nascimento:
          type: string
          format: date
          nullable: true
        genero:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/GeneroEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        telefones:
          type: array
          items:
            $ref: '#/components/schemas/Telefone'
          readOnly: true
        is_active:
          type: boolean
          readOnly: true
          description: Designates whether this user should be treated as active. Unselect
            this instead of deleting accounts.
        is_staff:
          type: boolean
          readOnly: true
          description: Designates whether the user can log into this admin site.
        is_superuser:
          type: boolean
          readOnly: true
          title: Superuser status
          description: Designates that this user has all permissions without explicitly
            assigning them.
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        last_login:
          type: string
          format: date-time
          readOnly: true
          nullable: true
    Plano:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        nome:
          type: string
          maxLength: 255
        descricao:
          type: string
        valor:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,2})?$
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
      required:
      - created
      - descricao
      - id
      - modified
      - nome
      - valor
    RecorrenciaEnum:
      enum:
      - mensal
      - semestral
      - anual
      type: string
      description: |-
        * `mensal` - Mensal
        * `semestral` - Semestral
        * `anual` - Anual
    ResetPassword:
      type: object
      properties:
        user_id:
          type: string
        timestamp:
          type: integer
        signature:
          type: string
        password:
          type: string
      required:
      - password
      - signature
      - timestamp
      - user_id
    Telefone:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        numero:
          type: string
          maxLength: 15
      required:
      - id
    TipoMovimentacaoEnum:
      enum:
      - 1
      - 2
      - 3
      - 4
      type: integer
      description: |-
        * `1` - Oferta
        * `2` - Venda
        * `3` - Distrato
        * `4` - Avulso
    UFsBrasil:
      type: object
      properties:
        value:
          type: string
        label:
          type: string
        pais:
          type: string
          default: BR
      required:
      - label
      - value
    UfEnum:
      enum:
      - AC
      - AL
      - AP
      - AM
      - BA
      - CE
      - DF
      - ES
      - GO
      - MA
      - MT
      - MS
      - MG
      - PA
      - PB
      - PR
      - PE
      - PI
      - RJ
      - RN
      - RS
      - RO
      - RR
      - SC
      - SP
      - SE
      - TO
      type: string
      description: |-
        * `AC` - Acre
        * `AL` - Alagoas
        * `AP` - Amapá
        * `AM` - Amazonas
        * `BA` - Bahia
        * `CE` - Ceará
        * `DF` - Distrito Federal
        * `ES` - Espírito Santo
        * `GO` - Goiás
        * `MA` - Maranhão
        * `MT` - Mato Grosso
        * `MS` - Mato Grosso do Sul
        * `MG` - Minas Gerais
        * `PA` - Pará
        * `PB` - Paraíba
        * `PR` - Paraná
        * `PE` - Pernambuco
        * `PI` - Piauí
        * `RJ` - Rio de Janeiro
        * `RN` - Rio Grande do Norte
        * `RS` - Rio Grande do Sul
        * `RO` - Rondônia
        * `RR` - Roraima
        * `SC` - Santa Catarina
        * `SP` - São Paulo
        * `SE` - Sergipe
        * `TO` - Tocantins
    User:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          maxLength: 255
        nome_completo:
          type: string
          maxLength: 255
        cpf:
          type: string
          maxLength: 14
        data_nascimento:
          type: string
          format: date
          nullable: true
        is_active:
          type: boolean
          description: Designates whether this user should be treated as active. Unselect
            this instead of deleting accounts.
        is_staff:
          type: boolean
          description: Designates whether the user can log into this admin site.
        is_superuser:
          type: boolean
          title: Superuser status
          description: Designates that this user has all permissions without explicitly
            assigning them.
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        last_login:
          type: string
          format: date-time
          nullable: true
      required:
      - created
      - email
      - id
      - modified
    UserLogin:
      type: object
      description: |-
        Default serializer used for user login. Please keep in mind that
        the authentication is done by separate function defined by
        :ref:`login-authenticator-setting` setting.

        By default :ref:`login-authenticator-setting` function will use
        :ref:`user-login-fields-setting` setting to extract the login field
        from the validated serializer data either by using the 'login' key
        (which is used here) or the specific login field name(s)
        (e.g. 'username', 'email').

        If you want different behavior, you need to
        override :ref:`login-authenticator-setting` in your settings.
      properties:
        login:
          type: string
          format: email
        password:
          type: string
          writeOnly: true
      required:
      - login
      - password
    UserProfile:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
        nome_completo:
          type: string
          maxLength: 255
        cpf:
          type: string
          maxLength: 14
        data_nascimento:
          type: string
          format: date
          nullable: true
        genero:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/GeneroEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        telefones:
          type: array
          items:
            $ref: '#/components/schemas/Telefone'
          readOnly: true
        is_active:
          type: boolean
          readOnly: true
          description: Designates whether this user should be treated as active. Unselect
            this instead of deleting accounts.
        is_staff:
          type: boolean
          readOnly: true
          description: Designates whether the user can log into this admin site.
        is_superuser:
          type: boolean
          readOnly: true
          title: Superuser status
          description: Designates that this user has all permissions without explicitly
            assigning them.
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          format: date-time
          readOnly: true
        last_login:
          type: string
          format: date-time
          readOnly: true
          nullable: true
      required:
      - created
      - email
      - id
      - is_active
      - is_staff
      - is_superuser
      - last_login
      - modified
      - telefones
    UserRegister:
      type: object
      description: |-
        Default serializer used for user registration. It will use these:

        * User fields
        * :ref:`user-hidden-fields-setting` setting
        * :ref:`user-public-fields-setting` setting

        to automagically generate the required serializer fields.
      properties:
        password:
          type: string
          maxLength: 128
        email:
          type: string
          format: email
          maxLength: 255
        nome_completo:
          type: string
          maxLength: 255
      required:
      - email
      - password
    UserResetPasswordLink:
      type: object
      description: |-
        Default serializer used for sending reset password link.

        It will use :ref:`send-reset-password-link-serializer-use-email-setting`
        setting.
      properties:
        login:
          type: string
      required:
      - login
    VerifyEmail:
      type: object
      properties:
        user_id:
          type: string
        email:
          type: string
          format: email
        timestamp:
          type: integer
        signature:
          type: string
      required:
      - email
      - signature
      - timestamp
      - user_id
    VerifyRegistration:
      type: object
      properties:
        user_id:
          type: string
        timestamp:
          type: integer
        signature:
          type: string
      required:
      - signature
      - timestamp
      - user_id
  securitySchemes:
    jwtAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
