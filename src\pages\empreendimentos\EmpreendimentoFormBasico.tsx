import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { empreendimentoService } from '../../services/empreendimentoService';
import { incorporadoraService } from '../../services/incorporadoraService';
import { enderecoService } from '../../services/enderecoService';
import { EmpreendimentoCreate, EnderecoCreate } from '../../types/empreendimento';
import { Incorporadora } from '../../types/incorporadora';

interface EmpreendimentoFormBasicoProps {
  onNext: (data: EmpreendimentoCreate) => void;
  initialData?: Partial<EmpreendimentoCreate>;
}

// Tipo específico para o estado do formulário
interface FormData {
  nome: string;
  incorporadora: number;
  data_entrega: string;
  data_inicio_comercializacao: string;
  observacao: string;
  area_total: number;
  area_construida: number;
  is_lancamento: boolean;
  endereco: {
    logradouro: {
      cidade: number;
      zipcode: string;
      nome: string;
      bairro: string;
    };
    numero?: number;
    complemento: string;
    ponto_referencia: string;
    latitude: string;
    longitude: string;
  };
}

const EmpreendimentoFormBasico: React.FC<EmpreendimentoFormBasicoProps> = ({ 
  onNext, 
  initialData 
}) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [cidades, setCidades] = useState<any[]>([]);
  const [incorporadoras, setIncorporadoras] = useState<Incorporadora[]>([]);
const [formData, setFormData] = useState<FormData>({
  nome: '',
  incorporadora: 0,
  data_entrega: '',
  data_inicio_comercializacao: '',
  observacao: '',
  area_total: 0,
  area_construida: 0,
  is_lancamento: false,
  endereco: {
    logradouro: {
      cidade: 0,
      zipcode: '',
      nome: '',
      bairro: ''
    },
      numero: undefined,
      complemento: '',
      ponto_referencia: '',
      latitude: '',
      longitude: ''
    }
  });

  useEffect(() => {
    fetchCidades();
    fetchIncorporadoras();
    
    // Se há dados iniciais, converter para o formato do formulário
    if (initialData) {
      
      if (typeof initialData.endereco === 'number') {
        // Se é um ID, não podemos usar diretamente no formulário
        console.warn('Endereço é um ID, não pode ser editado diretamente');
  
        // Apenas preencher os outros campos
        setFormData(prev => ({
          ...prev,
          nome: initialData.nome || '',
          incorporadora: initialData.incorporadora || 0,
          area_total: initialData.area_total || 0,
          area_construida: initialData.area_construida || 0,
          is_lancamento: initialData.is_lancamento || false,
          data_entrega: initialData.data_entrega || '',
          data_inicio_comercializacao: initialData.data_inicio_comercializacao || '',
          observacao: initialData.observacao || ''
        }));
      } else if (initialData.endereco && typeof initialData.endereco === 'object' && 'logradouro' in initialData.endereco) {
        // Se é um objeto EnderecoCreate, usar diretamente
        const enderecoData = initialData.endereco as EnderecoCreate;
      
        setFormData(prev => ({
          ...prev,
          nome: initialData.nome || '',
          incorporadora: initialData.incorporadora || 0,
          area_total: initialData.area_total || 0,
          area_construida: initialData.area_construida || 0,
          is_lancamento: initialData.is_lancamento || false,
          data_entrega: initialData.data_entrega || '',
          data_inicio_comercializacao: initialData.data_inicio_comercializacao || '',
          observacao: initialData.observacao || '',
          endereco: {
            logradouro: {
              cidade: enderecoData.logradouro.cidade,
              zipcode: enderecoData.logradouro.zipcode,
              nome: enderecoData.logradouro.nome,
              bairro: enderecoData.logradouro.bairro
            },
            numero: enderecoData.numero,
            complemento: enderecoData.complemento || '',
            ponto_referencia: enderecoData.ponto_referencia || '',
            latitude: enderecoData.latitude || '',
            longitude: enderecoData.longitude || ''
          }
        }));
      } else {
        // Se não há endereço ou formato desconhecido, preencher apenas os outros campos
        setFormData(prev => ({
          ...prev,
          nome: initialData.nome || '',
          incorporadora: initialData.incorporadora || 0,
          area_total: initialData.area_total || 0,
          area_construida: initialData.area_construida || 0,
          is_lancamento: initialData.is_lancamento || false,
          data_entrega: initialData.data_entrega || '',
          data_inicio_comercializacao: initialData.data_inicio_comercializacao || '',
          observacao: initialData.observacao || ''
        }));
      }
    }
  }, [initialData]);

  const fetchCidades = async () => {
    try {
      const data = await empreendimentoService.listCidades();
      setCidades(data);
    } catch (error) {
      console.error('Erro ao buscar cidades:', error);
    }
  };

  const fetchIncorporadoras = async () => {
    try {
      const data = await incorporadoraService.listarParaSelecao();
      setIncorporadoras(data);
    } catch (error) {
      console.error('Erro ao buscar incorporadoras:', error);
    }
  };

  const handleCepSearch = async (cep: string) => {
    if (cep.length === 8) {
      try {
        const data = await enderecoService.searchLogradouroByZipcode(cep);
        if (data.results && data.results.length > 0) {
          const endereco = data.results[0];
          
          setFormData(prev => ({
            ...prev,
            endereco: {
              ...prev.endereco,
              logradouro: {
                cidade: endereco.cidade?.id || endereco.cidade || 0,
                zipcode: endereco.zipcode || cep,
                nome: endereco.nome || '',
                bairro: endereco.bairro || '',
                
              }
            }
          }));
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error);
      }
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('logradouro.')) {
      const child = field.replace('logradouro.', '');
      setFormData(prev => ({
        ...prev,
        endereco: {
          ...prev.endereco,
          logradouro: {
            ...prev.endereco.logradouro,
            [child]: value
          }
        }
      }));
    } else if (field.includes('endereco.')) {
      const child = field.replace('endereco.', '');
      setFormData(prev => ({
        ...prev,
        endereco: {
          ...prev.endereco,
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    
    // Validação básica dos campos obrigatórios
    const errors: string[] = [];
    
    if (!formData.nome || formData.nome.trim() === '') {
      errors.push('Nome do empreendimento é obrigatório');
    }
    
    if (!formData.incorporadora || formData.incorporadora === 0) {
      errors.push('Incorporadora é obrigatória');
    }
    
    if (!formData.endereco.logradouro.cidade || formData.endereco.logradouro.cidade === 0) {
      errors.push('Cidade é obrigatória');
    }
    
    if (!formData.endereco.logradouro.zipcode || formData.endereco.logradouro.zipcode.trim() === '') {
      errors.push('CEP é obrigatório');
    } else if (formData.endereco.logradouro.zipcode.length !== 8) {
      errors.push('CEP deve ter 8 dígitos');
    }
    
    if (!formData.endereco.logradouro.nome || formData.endereco.logradouro.nome.trim() === '') {
      errors.push('Logradouro é obrigatório');
    }
    
    if (!formData.endereco.logradouro.bairro || formData.endereco.logradouro.bairro.trim() === '') {
      errors.push('Bairro é obrigatório');
    }
    
    if (errors.length > 0) {
      alert(`Por favor, corrija os seguintes erros:\n\n${errors.join('\n')}`);
      return;
    }

    setLoading(true);
    try {
      // Preparar dados do endereço (sem criar na API ainda)
      const enderecoData = {
        logradouro: {
          cidade: formData.endereco.logradouro.cidade,
          zipcode: formData.endereco.logradouro.zipcode,
          nome: formData.endereco.logradouro.nome,
          bairro: formData.endereco.logradouro.bairro
        },
        numero: formData.endereco.numero || undefined,
        complemento: formData.endereco.complemento || '',
        ponto_referencia: formData.endereco.ponto_referencia || '',
        latitude: formData.endereco.latitude || '0',
        longitude: formData.endereco.longitude || '0'
      };



      // Preparar dados do empreendimento com o objeto endereço (não ID)
      const empreendimentoData: any = {
        nome: formData.nome.trim(),
        incorporadora: formData.incorporadora,
        endereco: enderecoData // Passar objeto, não ID
      };

            // Adicionar area_total e area_construida apenas se forem maiores que 0
      if (formData.area_total && formData.area_total > 0) {
        empreendimentoData.area_total = formData.area_total;
      }

      if (formData.area_construida && formData.area_construida > 0) {
        empreendimentoData.area_construida = formData.area_construida;
      }
      
      empreendimentoData.is_lancamento = formData.is_lancamento;

      // Adicionar campos opcionais apenas se existirem
      if (formData.data_entrega && formData.data_entrega.trim() !== '') {
        empreendimentoData.data_entrega = formData.data_entrega;
      }
      if (formData.data_inicio_comercializacao && formData.data_inicio_comercializacao.trim() !== '') {
        empreendimentoData.data_inicio_comercializacao = formData.data_inicio_comercializacao;
      }
      if (formData.observacao && formData.observacao.trim() !== '') {
        empreendimentoData.observacao = formData.observacao;
      }

      
      onNext(empreendimentoData);
    } catch (error: any) {
      console.error('Erro ao processar dados:', error);
      alert('Erro ao processar dados do empreendimento. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-6">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Informações Básicas */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Informações Básicas</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome do Empreendimento *
              </label>
              <input
                type="text"
                value={formData.nome}
                onChange={(e) => handleInputChange('nome', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nome do empreendimento"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Incorporadora/Construtora *
              </label>
              <select
                value={formData.incorporadora}
                onChange={(e) => handleInputChange('incorporadora', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Selecione uma incorporadora</option>
                {incorporadoras.map((incorporadora) => (
                  <option key={incorporadora.id} value={incorporadora.id}>
                    {incorporadora.empresa?.nome_fantasia || `Incorporadora ${incorporadora.id}`}
                  </option>
                ))}
              </select>
            </div>

              {/* Área Total */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Área Total (m²)
                  </label>
                  <input
                    type="number"
                    value={formData.area_total}
                    onChange={(e) => handleInputChange('area_total', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                    min="0"
                  />
                </div>

                {/* Área Construída */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Área Construída (m²)
                  </label>
                  <input
                    type="number"
                    value={formData.area_construida}
                    onChange={(e) => handleInputChange('area_construida', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                    min="0"
                  />
                </div>

                {/* É Lançamento? */}
                <div className="flex items-center">
                  <input
                    id="is_lancamento"
                    type="checkbox"
                    name="is_lancamento"
                    checked={formData.is_lancamento}
                    onChange={(e) => handleInputChange('is_lancamento', e.target.checked)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="is_lancamento" className="ml-2 block text-sm font-medium text-gray-700">
                    É Lançamento?
                  </label>
                </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Data de Entrega
              </label>
              <input
                type="date"
                value={formData.data_entrega}
                onChange={(e) => handleInputChange('data_entrega', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Data de Início da Comercialização
              </label>
              <input
                type="date"
                value={formData.data_inicio_comercializacao}
                onChange={(e) => handleInputChange('data_inicio_comercializacao', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Observações
              </label>
              <textarea
                value={formData.observacao}
                onChange={(e) => handleInputChange('observacao', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Observações sobre o empreendimento"
                rows={3}
              />
            </div>
          </div>
        </div>

        {/* Endereço */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Endereço</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                CEP *
              </label>
              <input
                type="text"
                value={formData.endereco.logradouro.zipcode}
                onChange={(e) => {
                  const cep = e.target.value.replace(/\D/g, '');
                  handleInputChange('logradouro.zipcode', cep);
                  if (cep.length === 8) {
                    handleCepSearch(cep);
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="00000000"
                maxLength={8}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Logradouro *
              </label>
              <input
                type="text"
                value={formData.endereco.logradouro.nome}
                onChange={(e) => handleInputChange('logradouro.nome', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nome da rua/avenida"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Número
              </label>
              <input
                type="number"
                value={formData.endereco.numero || ''}
                onChange={(e) => handleInputChange('endereco.numero', e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="123"
                min="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bairro *
              </label>
              <input
                type="text"
                value={formData.endereco.logradouro.bairro}
                onChange={(e) => handleInputChange('logradouro.bairro', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nome do bairro"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cidade *
              </label>
              <select
                value={formData.endereco.logradouro.cidade}
                onChange={(e) => handleInputChange('logradouro.cidade', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Selecione uma cidade</option>
                {cidades.map((cidade) => (
                  <option key={cidade.id} value={cidade.id}>
                    {cidade.nome} - {cidade.uf}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Complemento
              </label>
              <input
                type="text"
                value={formData.endereco.complemento}
                onChange={(e) => handleInputChange('endereco.complemento', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Apartamento, sala, etc."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ponto de Referência
              </label>
              <input
                type="text"
                value={formData.endereco.ponto_referencia}
                onChange={(e) => handleInputChange('endereco.ponto_referencia', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Próximo ao shopping, etc."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Latitude
              </label>
              <input
                type="text"
                value={formData.endereco.latitude}
                onChange={(e) => handleInputChange('endereco.latitude', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="-23.5505"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitude
              </label>
              <input
                type="text"
                value={formData.endereco.longitude}
                onChange={(e) => handleInputChange('endereco.longitude', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="-46.6333"
              />
            </div>
          </div>
        </div>

        {/* Botões */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/empreendimentos')}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Processando...' : 'Próximo'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EmpreendimentoFormBasico; 