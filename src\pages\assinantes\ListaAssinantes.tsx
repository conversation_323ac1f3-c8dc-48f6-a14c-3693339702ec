import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { PaginatedTable } from '../../components/Table/PaginatedTable';
import { Assinante } from '../../types/Assinante';
import { assinanteService } from '../../services/assinanteService';

export const ListaAssinantes: React.FC = () => {
  const [assinantes, setAssinantes] = useState<Assinante[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [filters, setFilters] = useState({
    search: '',
    tipoPlano: '',
    bloqueado: '' as 'true' | 'false' | '',
  });

  const fetchAssinantes = async () => {
    try {
      setLoading(true);
      // Ponto de integração: Chama o serviço para listar assinantes do backend.
      // Certifique-se de que o backend suporta paginação e os filtros de busca, tipoPlano e bloqueado.
      const response = await assinanteService.listar(
        pageSize,
        (currentPage - 1) * pageSize,
        filters.search,
        filters.tipoPlano,
        filters.bloqueado === 'true' ? true : filters.bloqueado === 'false' ? false : undefined
      );
      setAssinantes(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Erro ao buscar assinantes:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAssinantes();
  }, [currentPage, filters]);

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setCurrentPage(1);
  };

  const limparFiltros = () => {
    setFilters({ search: '', tipoPlano: '', bloqueado: '' });
    setCurrentPage(1);
  };

  const handleBloquearDesbloquear = async (id: number, isBloqueado: boolean) => {
    try {
      // Ponto de integração: Chama o serviço para bloquear/desbloquear o assinante no backend.
      // O backend deve ter endpoints específicos para essas ações.
      if (isBloqueado) {
        await assinanteService.desbloquear(id);
      } else {
        await assinanteService.bloquear(id);
      }
      fetchAssinantes(); // Recarregar a lista após a ação
    } catch (error) {
      console.error('Erro ao bloquear/desbloquear assinante:', error);
    }
  };

  const handleExcluir = async (id: number) => {
    if (window.confirm('Tem certeza que deseja excluir este assinante?')) {
      try {
        // Ponto de integração: Chama o serviço para excluir o assinante no backend.
        await assinanteService.excluir(id);
        fetchAssinantes(); // Recarregar a lista após a exclusão
      } catch (error) {
        console.error('Erro ao excluir assinante:', error);
      }
    }
  };

  const columns = [
    {
      key: 'nome_completo' as keyof Assinante,
      title: 'Nome',
      render: (value: any, record: Assinante) => (
        <span className="font-medium text-gray-900">{record.nome_completo}</span>
      ),
    },
    {
      key: 'email' as keyof Assinante,
      title: 'Email',
      render: (value: any, record: Assinante) => (
        <span>{record.email || 'Não informado'}</span>
      ),
    },
    {
      key: 'acoes' as keyof Assinante,
      title: 'Ações',
      render: (value: any, record: Assinante) => (
        <div className="flex space-x-2">
          <Link
            to={`/assinantes/${record.id}/editar`}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
          >
            Editar
          </Link>
          <button
            onClick={() => handleExcluir(record.id)}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            Excluir
          </button>
          <button
            onClick={() => handleBloquearDesbloquear(record.id, record.bloqueado)}
            className={`px-3 py-1 text-white rounded text-sm ${record.bloqueado ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-green-500 hover:bg-green-600'}`}
          >
            {record.bloqueado ? 'Desbloquear' : 'Bloquear'}
          </button>
        </div>
      ),
    },
    {
      key: 'contrato' as keyof Assinante,
      title: 'Contrato',
      render: (value: any, record: Assinante) => (
        <div className="flex space-x-2">
          <Link
            to={`/assinantes/${record.id}/financeiro`}
            className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
          >
            Financeiro
          </Link>
          <Link
            to={`/assinantes/${record.id}/plano`}
            className="px-3 py-1 bg-indigo-500 text-white rounded hover:bg-indigo-600 text-sm"
          >
            Plano
          </Link>
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Lista de Assinantes</h1>
        <Link
          to="/assinantes/novo"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Novo Assinante
        </Link>
      </div>

      {/* Filtros */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-lg font-semibold mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome/Email do Assinante
            </label>
            <input
              type="text"
              placeholder="Digite o nome ou email..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipo de Plano
            </label>
            <select
              value={filters.tipoPlano}
              onChange={(e) => handleFilterChange('tipoPlano', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todos</option>
              {/* Ponto de integração: Adicione opções de tipo de plano aqui, idealmente buscando-as do backend. [Backend] */}
              <option value="BASICO">Básico</option>
              <option value="PREMIUM">Premium</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={filters.bloqueado}
              onChange={(e) => handleFilterChange('bloqueado', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todos</option>
              <option value="false">Ativo</option>
              <option value="true">Bloqueado</option>
            </select>
          </div>
        </div>

        <div className="mt-4">
          <button
            onClick={limparFiltros}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Limpar Filtros
          </button>
        </div>
      </div>

      {/* Tabela */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <PaginatedTable
          data={assinantes}
          columns={columns}
          loading={loading}
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / pageSize)}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  );
};
