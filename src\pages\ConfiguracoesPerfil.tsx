import React, { useState, useEffect } from 'react';
import { authService } from '../services/auth';
import { telefoneService } from '../services/telefoneService';
import { User, UserProfile, ChangePassword } from '../types/auth';
import { Telefone, TelefoneInput } from '../types/telefone';

export const ConfiguracoesPerfil: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Estados para edição do perfil
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileData, setProfileData] = useState<UserProfile>({
    nome_completo: '',
    cpf: '',
    data_nascimento: '',
    genero: ''
  });
  
  // Estados para alteração de senha
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordData, setPasswordData] = useState<ChangePassword>({
    old_password: '',
    password: '',
    password_confirm: ''
  });

  // Estados para telefones
  const [telefones, setTelefones] = useState<Telefone[]>([]);
  const [isAddingTelefone, setIsAddingTelefone] = useState(false);
  const [novoTelefone, setNovoTelefone] = useState('');

  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const userData = await authService.getProfile();
      setUser(userData);
      setProfileData({
        nome_completo: userData.nome_completo,
        cpf: userData.cpf || '',
        data_nascimento: userData.data_nascimento || '',
        genero: userData.genero || ''
      });

      // Carregar telefones do usuário
      try {
        const telefonesData = await telefoneService.listar(userData.id);
        setTelefones(telefonesData.results);
      } catch (err) {
        console.error('Erro ao carregar telefones:', err);
      }
    } catch (err) {
      setError('Erro ao carregar dados do perfil');
    } finally {
      setLoading(false);
    }
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);
      setSuccess(null);
      
      const updatedUser = await authService.updateProfile(profileData);
      setUser(updatedUser);
      setIsEditingProfile(false);
      setSuccess('Perfil atualizado com sucesso!');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Erro ao atualizar perfil');
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (passwordData.password !== passwordData.password_confirm) {
      setError('As senhas não coincidem');
      return;
    }

    if (passwordData.password.length < 8) {
      setError('A nova senha deve ter pelo menos 8 caracteres');
      return;
    }

    try {
      setError(null);
      setSuccess(null);
      
      await authService.changePassword(passwordData);
      setIsChangingPassword(false);
      setPasswordData({
        old_password: '',
        password: '',
        password_confirm: ''
      });
      setSuccess('Senha alterada com sucesso!');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Erro ao alterar senha');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCPF = (cpf: string | null | undefined) => {
    // Verifica se o CPF existe e é uma string
    if (!cpf || typeof cpf !== 'string') {
      return '';
    }
    
    // Remove caracteres não numéricos
    const cpfLimpo = cpf.replace(/\D/g, '');
    
    // Verifica se tem 11 dígitos para aplicar a máscara
    if (cpfLimpo.length === 11) {
      return cpfLimpo.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    }
    
    // Se não tem 11 dígitos, retorna apenas os números
    return cpfLimpo;
  };

  const handleCPFChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Remove caracteres não numéricos
    const cpfLimpo = value.replace(/\D/g, '');
    // Limita a 11 dígitos
    if (cpfLimpo.length <= 11) {
      setProfileData({ ...profileData, cpf: cpfLimpo || '' });
    }
  };

  const formatarTelefone = (numero: string) => {
    // Remove caracteres não numéricos
    const numeroLimpo = numero.replace(/\D/g, '');
    
    // Aplica máscara baseada no tamanho
    if (numeroLimpo.length === 11) {
      return numeroLimpo.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (numeroLimpo.length === 10) {
      return numeroLimpo.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    
    return numeroLimpo;
  };

  const handleTelefoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Remove caracteres não numéricos
    const numeroLimpo = value.replace(/\D/g, '');
    // Limita a 11 dígitos
    if (numeroLimpo.length <= 11) {
      setNovoTelefone(numeroLimpo);
    }
  };

  const handleAddTelefone = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!novoTelefone || !user) return;

    try {
      const telefoneData: TelefoneInput = {
        usuario: user.id,
        numero: novoTelefone
      };
      
      const novoTelefoneCriado = await telefoneService.criar(telefoneData);
      setTelefones([...telefones, novoTelefoneCriado]);
      setNovoTelefone('');
      setIsAddingTelefone(false);
      setSuccess('Telefone adicionado com sucesso!');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Erro ao adicionar telefone');
    }
  };

  const handleDeleteTelefone = async (id: number) => {
    if (!window.confirm('Tem certeza que deseja excluir este telefone?')) {
      return;
    }

    try {
      await telefoneService.excluir(id);
      setTelefones(telefones.filter(tel => tel.id !== id));
      setSuccess('Telefone removido com sucesso!');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Erro ao remover telefone');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#29306a]"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-600">Erro ao carregar dados do usuário</div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-[#29306a] mb-2">Configurações do Perfil</h1>
        <p className="text-gray-600">Gerencie suas informações pessoais e configurações de conta</p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-600">{success}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Informações Pessoais - Ocupa 2 colunas */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-[#29306a]">Informações Pessoais</h2>
            <button
              onClick={() => setIsEditingProfile(!isEditingProfile)}
              className="px-4 py-2 text-sm font-medium text-white bg-[#29306a] rounded-md hover:bg-[#1e2347] transition-colors"
            >
              {isEditingProfile ? 'Cancelar' : 'Editar'}
            </button>
          </div>

          {!isEditingProfile ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <p className="text-gray-900">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome Completo</label>
                <p className="text-gray-900">{user.nome_completo}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">CPF</label>
                <p className="text-gray-900">
                  {user.cpf ? formatCPF(user.cpf) : 'Não informado'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Data de Nascimento</label>
                <p className="text-gray-900">
                  {user.data_nascimento 
                    ? new Date(user.data_nascimento).toLocaleDateString('pt-BR')
                    : 'Não informado'
                  }
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Gênero</label>
                <p className="text-gray-900">
                  {user.genero 
                    ? user.genero === 'MASCULINO' ? 'Masculino' 
                      : user.genero === 'FEMININO' ? 'Feminino' 
                      : user.genero === 'OUTRO' ? 'Outro' 
                      : user.genero
                    : 'Não informado'
                  }
                </p>
              </div>
            </div>
          ) : (
            <form onSubmit={handleProfileSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nome Completo</label>
                  <input
                    type="text"
                    value={profileData.nome_completo}
                    onChange={(e) => setProfileData({ ...profileData, nome_completo: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#29306a] focus:border-transparent"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">CPF</label>
                  <input
                    type="text"
                    value={formatCPF(profileData.cpf)}
                    onChange={handleCPFChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#29306a] focus:border-transparent"
                    placeholder="000.000.000-00"
                    maxLength={14}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Data de Nascimento</label>
                  <input
                    type="date"
                    value={profileData.data_nascimento}
                    onChange={(e) => setProfileData({ ...profileData, data_nascimento: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#29306a] focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Gênero</label>
                  <select
                    value={profileData.genero}
                    onChange={(e) => setProfileData({ ...profileData, genero: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#29306a] focus:border-transparent"
                  >
                    <option value="">Selecione...</option>
                    <option value="MASCULINO">Masculino</option>
                    <option value="FEMININO">Feminino</option>
                    <option value="OUTRO">Outro</option>
                  </select>
                </div>
              </div>
              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-[#29306a] rounded-md hover:bg-[#1e2347] transition-colors"
                >
                  Salvar
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setIsEditingProfile(false);
                    setProfileData({
                      nome_completo: user.nome_completo,
                      cpf: user.cpf || '',
                      data_nascimento: user.data_nascimento || '',
                      genero: user.genero || ''
                    });
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Cancelar
                </button>
              </div>
            </form>
          )}
        </div>

        {/* Coluna da Direita - Telefones e Alterar Senha */}
        <div className="space-y-8">
          {/* Telefones */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-[#29306a]">Telefones</h2>
              <button
                onClick={() => setIsAddingTelefone(!isAddingTelefone)}
                className="px-3 py-1 text-sm font-medium text-white bg-[#32bef0] rounded-md hover:bg-[#2ba8d8] transition-colors"
              >
                {isAddingTelefone ? 'Cancelar' : '+'}
              </button>
            </div>

            {isAddingTelefone && (
              <form onSubmit={handleAddTelefone} className="mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={formatarTelefone(novoTelefone)}
                    onChange={handleTelefoneChange}
                    placeholder="(00) 00000-0000"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#32bef0] focus:border-transparent text-sm"
                    maxLength={15}
                    required
                  />
                  <button
                    type="submit"
                    className="px-3 py-2 text-sm font-medium text-white bg-[#32bef0] rounded-md hover:bg-[#2ba8d8] transition-colors"
                  >
                    +
                  </button>
                </div>
              </form>
            )}

            <div className="space-y-2">
              {telefones.length === 0 ? (
                <p className="text-gray-500 text-sm">Nenhum telefone cadastrado</p>
              ) : (
                telefones.map((telefone) => (
                  <div key={telefone.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                    <span className="text-gray-900 text-sm">{formatarTelefone(telefone.numero)}</span>
                    <button
                      onClick={() => handleDeleteTelefone(telefone.id)}
                      className="text-red-600 hover:text-red-800 text-xs font-medium"
                    >
                      Remover
                    </button>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Alterar Senha */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-[#29306a]">Alterar Senha</h2>
              <button
                onClick={() => setIsChangingPassword(!isChangingPassword)}
                className="px-3 py-1 text-sm font-medium text-white bg-[#f55eeb] rounded-md hover:bg-[#e54dd8] transition-colors"
              >
                {isChangingPassword ? 'Cancelar' : 'Alterar'}
              </button>
            </div>

            {isChangingPassword && (
              <form onSubmit={handlePasswordSubmit} className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Senha Atual</label>
                  <input
                    type="password"
                    value={passwordData.old_password}
                    onChange={(e) => setPasswordData({ ...passwordData, old_password: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#f55eeb] focus:border-transparent text-sm"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nova Senha</label>
                  <input
                    type="password"
                    value={passwordData.password}
                    onChange={(e) => setPasswordData({ ...passwordData, password: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#f55eeb] focus:border-transparent text-sm"
                    required
                    minLength={8}
                  />
                  <p className="text-xs text-gray-500 mt-1">Mínimo 8 caracteres</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Confirmar Nova Senha</label>
                  <input
                    type="password"
                    value={passwordData.password_confirm}
                    onChange={(e) => setPasswordData({ ...passwordData, password_confirm: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#f55eeb] focus:border-transparent text-sm"
                    required
                  />
                </div>
                <div className="flex space-x-3 pt-3">
                  <button
                    type="submit"
                    className="px-3 py-2 text-sm font-medium text-white bg-[#f55eeb] rounded-md hover:bg-[#e54dd8] transition-colors"
                  >
                    Alterar
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setIsChangingPassword(false);
                      setPasswordData({
                        old_password: '',
                        password: '',
                        password_confirm: ''
                      });
                    }}
                    className="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    Cancelar
                  </button>
                </div>
              </form>
            )}

            {!isChangingPassword && (
              <div className="text-gray-600 text-sm">
                <p>Clique em "Alterar" para modificar sua senha de acesso.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Informações da Conta - Largura Total */}
      <div className="mt-8 bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-[#29306a] mb-6">Informações da Conta</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status da Conta</label>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              user.is_active 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {user.is_active ? 'Ativa' : 'Inativa'}
            </span>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Staff</label>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              user.is_staff 
                ? 'bg-blue-100 text-blue-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {user.is_staff ? 'Sim' : 'Não'}
            </span>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Superusuário</label>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              user.is_superuser 
                ? 'bg-purple-100 text-purple-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {user.is_superuser ? 'Sim' : 'Não'}
            </span>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">ID do Usuário</label>
            <p className="text-gray-900">{user.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Data de Criação</label>
            <p className="text-gray-900">{formatDate(user.created)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Última Atualização</label>
            <p className="text-gray-900">{formatDate(user.modified)}</p>
          </div>
        </div>
      </div>
    </div>
  );
}; 