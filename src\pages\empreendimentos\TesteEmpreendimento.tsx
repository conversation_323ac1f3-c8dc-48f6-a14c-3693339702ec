import React, { useState, useEffect } from 'react';
import { empreendimentoService } from '../../services/empreendimentoService';

const TesteEmpreendimento: React.FC = () => {
  const [resultado, setResultado] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testarEndpoints = async () => {
    setLoading(true);
    setResultado('');

    try {
      // Teste 1: Listar empreendimentos
      setResultado(prev => prev + '=== TESTE 1: LISTAR EMPREENDIMENTOS ===\n');
      const listResponse = await empreendimentoService.list({ limit: 10 });
      setResultado(prev => prev + `Total: ${listResponse.count}\n`);
      setResultado(prev => prev + `Primeiros IDs: ${listResponse.results.map(emp => emp.id).join(', ')}\n\n`);

      // Teste 2: Tentar buscar empreendimento específico
      if (listResponse.results.length > 0) {
        const primeiroId = listResponse.results[0].id;
        setResultado(prev => prev + `=== TESTE 2: BUSCAR EMPREENDIMENTO ID ${primeiroId} ===\n`);
        
        try {
          const emp = await empreendimentoService.getById(primeiroId);
          setResultado(prev => prev + `✅ Sucesso: ${emp.nome}\n\n`);
        } catch (error: any) {
          setResultado(prev => prev + `❌ Erro: ${error.message}\n`);
          setResultado(prev => prev + `Status: ${error.response?.status}\n`);
          setResultado(prev => prev + `Data: ${JSON.stringify(error.response?.data)}\n\n`);
        }
      }

      // Teste 3: Tentar buscar ID 13 especificamente
      setResultado(prev => prev + '=== TESTE 3: BUSCAR EMPREENDIMENTO ID 13 ===\n');
      try {
        const emp13 = await empreendimentoService.getById(13);
        setResultado(prev => prev + `✅ Sucesso: ${emp13.nome}\n\n`);
      } catch (error: any) {
        setResultado(prev => prev + `❌ Erro: ${error.message}\n`);
        setResultado(prev => prev + `Status: ${error.response?.status}\n`);
        setResultado(prev => prev + `Data: ${JSON.stringify(error.response?.data)}\n\n`);
      }

    } catch (error: any) {
      setResultado(prev => prev + `❌ Erro geral: ${error.message}\n`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Teste de Endpoints de Empreendimento</h1>
      
      <button
        onClick={testarEndpoints}
        disabled={loading}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 mb-4"
      >
        {loading ? 'Testando...' : 'Executar Testes'}
      </button>

      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-bold mb-2">Resultados:</h2>
        <pre className="whitespace-pre-wrap text-sm">{resultado || 'Clique em "Executar Testes" para começar'}</pre>
      </div>
    </div>
  );
};

export default TesteEmpreendimento; 