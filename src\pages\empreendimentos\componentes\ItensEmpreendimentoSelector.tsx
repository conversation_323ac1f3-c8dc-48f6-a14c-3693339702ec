import React from 'react';
import { useState, useEffect, useMemo } from 'react';
// 1. Importe o react-select e seus tipos
import Select, { MultiValue } from 'react-select';
import { ItemEmpreendimento } from '../../../types/empreendimento';
import { ItemEmpreendimentoService } from '../../../services/empreendimentoService';
import { User } from '../../../types/auth';
import { authService } from '../../../services/auth';

interface ItensEmpreendimentoSelectorProps {
  selectedItens: number[];
  onSelectionChange: (newSelectedItens: number[]) => void;
  onManageItensClick: () => void;
  successButtonStyle: string;
}

// Formato que o react-select espera para as opções
interface SelectOption {
  value: number;
  label: string;
}

const ItensEmpreendimentoSelector: React.FC<ItensEmpreendimentoSelectorProps> = ({
  selectedItens,
  onSelectionChange,
  onManageItensClick,
  successButtonStyle,
}) => {
  const [allItems, setAllItems] = useState<ItemEmpreendimento[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);

  // A busca de dados e de usuário permanece a mesma
  useEffect(() => {
    const itemEmpreendimentoService = new ItemEmpreendimentoService();
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        // Busca o perfil e os itens em paralelo
        const [userProfile, allData] = await Promise.all([
            authService.getProfile(),
            itemEmpreendimentoService.getAllItens()
        ]);
        setUser(userProfile);
        setAllItems(allData);
      } catch (err) {
        console.error("Erro ao carregar dados:", err);
        setError("Não foi possível carregar os dados. Tente novamente.");
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  // 2. Transforme os itens da API em três listas de opções separadas, uma para cada categoria.
  const optionsByCategory = useMemo(() => {
    const lazerOptions: SelectOption[] = [];
    const segurancaOptions: SelectOption[] = [];
    const infraestruturaOptions: SelectOption[] = [];

    allItems.forEach(item => {
      const option = { value: Number(item.id), label: item.name };
      switch (item.tipo_item_empreendimento) {
        case 1: // Lazer
          lazerOptions.push(option);
          break;
        case 2: // Segurança
          segurancaOptions.push(option);
          break;
        case 3: // Infraestrutura
          infraestruturaOptions.push(option);
          break;
        default:
          break;
      }
    });

    return { lazerOptions, segurancaOptions, infraestruturaOptions };
  }, [allItems]);
  
  // 3. Crie um handler genérico para a mudança de seleção
  const handleSelectChange = (
    newValue: MultiValue<SelectOption>,
    categoryOptions: SelectOption[]
  ) => {
    // Pega os IDs da categoria que foi alterada
    const newSelectedIds = newValue.map(option => option.value);

    // Pega todos os IDs das outras categorias que já estavam selecionados
    const categoryIdsSet = new Set(categoryOptions.map(opt => opt.value));
    const otherCategoriesSelectedIds = selectedItens.filter(id => !categoryIdsSet.has(id));

    // Junta os IDs das outras categorias com os novos IDs da categoria atual
    onSelectionChange([...otherCategoriesSelectedIds, ...newSelectedIds]);
  };

  // 4. Função auxiliar para renderizar cada grupo de select (evita repetição de código)
  const renderSelectGroup = (
    title: string,
    options: SelectOption[],
    placeholder: string
  ) => {
    // Filtra as opções que devem aparecer como selecionadas neste select específico
    const selectedOptionsForThisGroup = options.filter(option =>
      selectedItens.includes(option.value)
    );

    return (
      <div>
        <h4 className="font-semibold text-gray-800 mb-3 border-b pb-2">{title}</h4>
        <Select
          isMulti
          options={options}
          value={selectedOptionsForThisGroup}
          onChange={(newValue) => handleSelectChange(newValue, options)}
          isLoading={loading}
          placeholder={placeholder}
          noOptionsMessage={() => "Nenhum item"}
          className="basic-multi-select"
          classNamePrefix="select"
          closeMenuOnSelect={false}
        />
      </div>
    );
  };

  return (
    <div className="bg-white shadow-md rounded-lg">
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Itens do Empreendimento</h3>
        {(user?.tipo === 'TACT') && (
          <button type="button" onClick={onManageItensClick} className={successButtonStyle}>
            Gerenciar Itens
          </button>
        )}
      </div>
      <div className="p-6">
        {error && <p className="text-center text-red-600">{error}</p>}
        {!error && (
            // 5. Renderiza o grid com um grupo de select para cada categoria
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {renderSelectGroup("Lazer", optionsByCategory.lazerOptions, "Itens de lazer...")}
                {renderSelectGroup("Segurança", optionsByCategory.segurancaOptions, "Itens de segurança...")}
                {renderSelectGroup("Infraestrutura", optionsByCategory.infraestruturaOptions, "Itens de infra...")}
            </div>
        )}
      </div>
    </div>
  );
};

export default ItensEmpreendimentoSelector;