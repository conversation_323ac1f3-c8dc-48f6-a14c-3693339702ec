# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Testing
coverage
.nyc_output

# Production
build
dist

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Version control
.git
.gitignore

# IDE
.idea
.vscode
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
LICENSE
*.md 