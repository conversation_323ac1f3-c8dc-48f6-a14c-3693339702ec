import { CrudService } from './crud';
import { Plano, PlanoInput, PlanoUpdateInput, PaginatedPlanoResponse } from '../types/plano';
import api from './api';

class PlanoService extends CrudService<Plano> {
  constructor() {
    super('/planos/');
  }

  async criar(plano: PlanoInput): Promise<Plano> {
    const response = await api.post<Plano>('/planos/', plano);
    return response.data;
  }

  async buscarPorId(id: number): Promise<Plano> {
    return this.get(id);
  }

  async atualizar(id: number, plano: PlanoUpdateInput): Promise<Plano> {
    return this.update(id, plano);
  }

  async atualizarParcial(id: number, plano: PlanoUpdateInput): Promise<Plano> {
    return this.update(id, plano);
  }

  async excluir(id: number): Promise<void> {
    return this.delete(id);
  }

  async listar(page: number = 1, limit: number = 10): Promise<PaginatedPlanoResponse> {
    const offset = (page - 1) * limit;
    const response = await api.get<PaginatedPlanoResponse>(`/planos/?offset=${offset}&limit=${limit}`);
    return response.data;
  }
}

export const planoService = new PlanoService(); 