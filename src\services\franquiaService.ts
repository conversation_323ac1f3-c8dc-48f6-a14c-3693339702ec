import api from './api';
import { NovoMembroPayload, PaginatedMembrosFranquia } from '../types/franquia';

export const franquiaService = {
  async adicionarMembro(franquiaId: number, data: NovoMembroPayload): Promise<any> {
    const response = await api.post(`/franquias/${franquiaId}/membros/`, data);
    return response.data;
  },

  async listarMembros(
    franquiaId: number,
    limit: number,
    offset: number,
    search?: string
  ): Promise<PaginatedMembrosFranquia> {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    if (search) {
      params.append('search', search);
    }

    const response = await api.get(`/franquias/${franquiaId}/membros/?${params.toString()}`);
    return response.data;
  }
};