import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { franqueadoService } from '../../services/franqueadoService';
import { FranqueadoForm } from './FranqueadoForm';
import { FranqueadoInput } from '../../types/franqueado';

export const EditarFranqueado: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [franqueado, setFranqueado] = useState<FranqueadoInput | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFranqueado = async () => {
      if (!id) {
        setError('ID do franqueado não fornecido');
        setLoading(false);
        return;
      }

      try {
        const franqueadoData = await franqueadoService.buscarPorId(parseInt(id));
        setFranqueado({
          nome: franqueadoData.nome,
          email: franqueadoData.email,
        });
      } catch (err) {
        console.error('Erro ao carregar franqueado:', err);
        setError('Erro ao carregar dados do franqueado');
      } finally {
        setLoading(false);
      }
    };

    fetchFranqueado();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#32bef0]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">{error}</h3>
                <button
                  onClick={() => navigate('/franqueados')}
                  className="mt-2 text-sm text-red-600 hover:text-red-500"
                >
                  Voltar para lista
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!franqueado) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="rounded-md bg-yellow-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Franqueado não encontrado
                </h3>
                <button
                  onClick={() => navigate('/franqueados')}
                  className="mt-2 text-sm text-yellow-600 hover:text-yellow-500"
                >
                  Voltar para lista
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-[#29306a]">Editar Franqueado</h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Atualize as informações do franqueado
          </p>
        </div>
        <FranqueadoForm 
          franqueado={franqueado} 
          isEditing={true} 
          franqueadoId={parseInt(id!)} 
        />
      </div>
    </div>
  );
}; 