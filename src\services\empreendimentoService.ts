import api from './api';
import { enderecoService } from './enderecoService';
import axios, { AxiosResponse } from 'axios';

import { 
  Empreendimento, 
  EmpreendimentoCreate, 
  EmpreendimentoUpdate, 
  PaginatedEmpreendimentoResponse,
  ParteEmpreendimento,
  ParteEmpreendimentoCreate,
  TipoEmpreendimento,
  TipoEmpreendimentoCreate,
  ItemEmpreendimento,
  ItemEmpreendimentoCreate,
  TipoParteEmpreendimento,
  TipoParteEmpreendimentoCreate,
  PaginatedResponse,
} from '../types/empreendimento';

export class EmpreendimentoService {
  private baseUrl = '/empreendimentos/';

  async list(params?: {
    limit?: number;
    offset?: number;
    search?: string;
    incorporadora?: number;
    cidade?: number;
  }): Promise<PaginatedEmpreendimentoResponse> {
    const response = await api.get(this.baseUrl, { params });
    return response.data;
  }

  async getById(id: number): Promise<Empreendimento> {

    
    try {
      const response = await api.get(`${this.baseUrl}${id}/`);
      return response.data;
    } catch (error: any) {
      console.error('Erro detalhado na busca do empreendimento:', error);
      console.error('Status:', error.response?.status);
      console.error('Data:', error.response?.data);
      console.error('Headers:', error.response?.headers);
      throw error;
    }
  }

  async get(id: number): Promise<Empreendimento> {
    return this.getById(id);
  }

  async create(empreendimento: EmpreendimentoCreate): Promise<Empreendimento> {
  try {
    const formData = new FormData();

    // Adiciona todos os campos simples
    Object.entries(empreendimento).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          // Para arrays (como tipo_empreendimento)
          value.forEach(v => formData.append(key, v.toString()));
        } else {
          formData.append(key, value as any);
        }
      }
    });

    const response = await api.post(this.baseUrl, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });

    return response.data;
  } catch (error: any) {
    console.error('=== ERRO DA API ===');
    console.error('Status:', error.response?.status);
    console.error('Dados do erro:', JSON.stringify(error.response?.data, null, 2));
    console.error('Mensagem:', error.response?.data?.message);
    console.error('Erros detalhados:', JSON.stringify(error.response?.data?.errors, null, 2));
    throw error;
  }
}


  async update(id: number, empreendimento: EmpreendimentoUpdate): Promise<Empreendimento> {
    const response = await api.put(`${this.baseUrl}${id}/`, empreendimento);
    return response.data;
  }

  async patch(id: number, empreendimento: Partial<EmpreendimentoUpdate>): Promise<Empreendimento> {
    const response = await api.patch(`${this.baseUrl}${id}/`, empreendimento);
    return response.data;
  }

  async delete(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}${id}/`);
  }

  async createWithParts(empreendimento: EmpreendimentoCreate, partes: ParteEmpreendimentoCreate[]): Promise<Empreendimento> {
    try {
      console.log('Dados do empreendimento:', empreendimento);
      
      // Se o endereço é um objeto, criar primeiro
      let enderecoId = empreendimento.endereco;
      if (typeof empreendimento.endereco === 'object') {

        
        const endereco = await enderecoService.createEndereco(empreendimento.endereco as any);
        enderecoId = endereco.id;
       
      }
      
      // Preparar dados do empreendimento com o ID do endereço
      const empreendimentoData = {
        ...empreendimento,
        endereco: enderecoId,
        heading: empreendimento.nome || 'Empreendimento',
        pitch: `Empreendimento ${empreendimento.nome || ''}`
      };
      

      console.log(empreendimentoData)
      const empreendimentoCriado = await this.create(empreendimentoData);
      
      
      for (const parte of partes) {
        try {
          
          
          // Garantir que o empreendimento_id seja enviado corretamente
          const parteComEmpreendimento = {
            ...parte,
            empreendimento: empreendimentoCriado.id
          };
          
         
          
          await this.createParteEmpreendimento(parteComEmpreendimento);
         
        } catch (parteError: any) {
          
          try {
            await this.delete(empreendimentoCriado.id);
            console.log('Empreendimento deletado devido ao erro na parte');
          } catch (deleteError) {
            console.error('Erro ao deletar empreendimento após falha na parte:', deleteError);
          }
          throw new Error(`Erro ao criar parte do empreendimento: ${parteError.message || 'Erro desconhecido'}`);
        }
      }

      return empreendimentoCriado;
    } catch (error: any) {
      console.error('Erro no createWithParts:', error);
      
      if (error.response) {
        throw error;
      } else if (error.message) {
        throw error;
      } else {
        throw new Error('Erro desconhecido ao criar empreendimento');
      }
    }
  }

  async createParteEmpreendimento(parte: ParteEmpreendimentoCreate): Promise<ParteEmpreendimento> {
    
    // Criar payload completo com todos os campos necessários
    const payloadCompleto = {
      nome: parte.nome,
      torre:parte.torre,
      area_privativa_m2: parte.area_privativa_m2,
      valor_imovel: parte.valor_imovel,
      forma_pagamento: parte.forma_pagamento,
      disponibilidade: parte.disponibilidade,
      quantidade_garagem: parte.quantidade_garagem,
      tipo_parte_empreendimento: parte.tipo_parte_empreendimento,
      empreendimento: parte.empreendimento,

      // Campos opcionais
      ...(parte.was_lancamento !== undefined && { was_lancamento: parte.was_lancamento }),
      ...(parte.ano_disponibilidade !== undefined && { ano_disponibilidade: parte.ano_disponibilidade }),
      ...(parte.mes_disponibilidade !== undefined && { mes_disponibilidade: parte.mes_disponibilidade }),
      ...(parte.origem_recurso !== undefined && { origem_recurso: parte.origem_recurso }),
      ...(parte.atributos_extras && { atributos_extras: parte.atributos_extras }),
      ...(parte.mcmv !== undefined && { mcmv: parte.mcmv }),
      ...(parte.tipo_produto !== undefined && { tipo_produto: parte.tipo_produto }),
    };
    
    
    try {
      const response = await api.post('/unidade/', payloadCompleto);
      return response.data;
    } catch (error: any) {
      console.error('Erro detalhado ao criar parte:', error);
      console.error('Response data:', error.response?.data);
      console.error('Response status:', error.response?.status);
      console.error('Response headers:', error.response?.headers);
      throw new Error(`Erro ao criar parte do empreendimento: ${error.message}`);
    }
  }

  async listPartesEmpreendimento(empreendimentoId: number): Promise<ParteEmpreendimento[]> {
    try {
      const response = await api.get('/unidade/', {
        params: { empreendimento: empreendimentoId }
      });
      
      // Filtrar novamente no frontend para garantir
      const partesFiltradas = response.data.results.filter((parte: any) => 
        parte.empreendimento === empreendimentoId
      );
      
      return partesFiltradas;
    } catch (error) {
      console.error('Erro ao listar partes:', error);
      throw error;
    }
  }

  async listCidades(): Promise<any[]> {
    const response = await api.get('/cidades/');
    return response.data.results;
  }
}

export class TipoParteEmpreendimentoService {

  async getTipos(): Promise<TipoParteEmpreendimento[]> {
    const response = await api.get('tipos-parte-empreendimento/');
    return response.data.results;
  }

  async getTipoById(id: number): Promise<TipoParteEmpreendimento> {
    const response = await api.get(`tipos-parte-empreendimento/${id}/`);
    return response.data;
  }

  async addTipo(tipo: TipoParteEmpreendimentoCreate): Promise<TipoParteEmpreendimento> {
    const response = await api.post('tipos-parte-empreendimento/', tipo);
    return response.data;
  }

  async updateTipo(id: number, tipo: Partial<TipoParteEmpreendimentoCreate>): Promise<TipoParteEmpreendimento> {
    const response = await api.put(`tipos-parte-empreendimento/${id}/`, tipo);
    return response.data;
  }

  async deleteTipo(id: number): Promise<void> {
    await api.delete(`tipos-parte-empreendimento/${id}/`);
  }
}
export class ItemEmpreendimentoService {
  
  async getItens(): Promise<ItemEmpreendimento[]> {
    const response = await api.get<PaginatedResponse<ItemEmpreendimento>>('item-empreendimento/');
    return response.data.results;
  }

  async getItemById(id: string): Promise<ItemEmpreendimento | null> {
    try {
      const response = await api.get<ItemEmpreendimento>(`${'item-empreendimento/'}${id}/`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar item por ID:', error);
      return null;
    }
  }

  async addItem(item: ItemEmpreendimentoCreate): Promise<ItemEmpreendimento> {
    const response = await api.post<ItemEmpreendimento>('item-empreendimento/', item);
    return response.data;
  }

  async updateItem(id: string, item: Partial<ItemEmpreendimentoCreate>): Promise<ItemEmpreendimento> {
    const response = await api.put<ItemEmpreendimento>(`${'item-empreendimento/'}${id}/`, item);
    return response.data;
  }

  async deleteItem(id: string): Promise<boolean> {
    try {
      await api.delete(`${'item-empreendimento/'}${id}/`);
      return true;
    } catch (error) {
      console.error('Erro ao deletar item:', error);
      return false;
    }
  }

  async getItensByCategoria(categoria: string): Promise<ItemEmpreendimento[]> {
    const response = await api.get<PaginatedResponse<ItemEmpreendimento>>('item-empreendimento/?tipo=' + categoria, {
    });
    
    return response.data.results;
  }
  
async getAllItens(): Promise<ItemEmpreendimento[]> {
  let url: string | null = 'item-empreendimento/?offset=0&limit=100';
  let allResults: ItemEmpreendimento[] = [];

  while (url) {
    // Tipagem explícita do response
    const response: AxiosResponse<PaginatedResponse<ItemEmpreendimento>> = 
      await api.get<PaginatedResponse<ItemEmpreendimento>>(url);
    
    allResults.push(...response.data.results);

    // Atualiza a URL para a próxima página (relativa)
    url = response.data.next 
      ? response.data.next.replace(api.defaults.baseURL || '', '') 
      : null;
  }

  return allResults;
}






}




export const empreendimentoService = new EmpreendimentoService();
