import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Login } from '../pages/Login';
import { Register } from '../pages/Register';

interface PublicRoutesProps {
  onLoginSuccess: () => void;
}

export const PublicRoutes: React.FC<PublicRoutesProps> = ({ onLoginSuccess }) => {
  return (
    <Routes>
      <Route 
        path="/login" 
        element={<Login onLoginSuccess={onLoginSuccess} />}
      />
      <Route 
        path="/register" 
        element={<Register />}
      />
      <Route path="*" element={<Navigate to="/login" replace />} />
    </Routes>
  );
}; 