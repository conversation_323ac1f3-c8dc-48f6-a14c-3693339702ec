import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import FuncionarioForm, { FormData } from './funcionarioForm';
import { useAuth } from '../../contexts/AuthContext';
import { franquiaService } from '../../services/franquiaService';
import { NovoMembroPayload } from '../../types/franquia';

const ALL_CARGOS = ['GESTOR', 'FINANCEIRO', 'CADASTRAMENTO', 'VENDEDOR'];
const GESTOR_CARGOS = ['FINANCEIRO', 'CADASTRAMENTO', 'VENDEDOR'];

export const Novofuncionario: React.FC = () => {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();
  const [formLoading, setFormLoading] = useState(false);

  const availableCargos = useMemo(() => {
    if (!user || !user.franquia_membership) return [];

    const userCargos = user.franquia_membership.cargos;

    if (userCargos.includes('ADMIN')) {
      return ALL_CARGOS;
    }

    if (userCargos.includes('GESTOR')) {
      return GESTOR_CARGOS;
    }

    return [];
  }, [user]);

  const handleSubmit = async (data: FormData) => {
    if (!user || !user.franquia_membership?.franquia_id) {
      alert('Erro: Informações do usuário ou da franquia não encontradas.');
      return;
    }

    setFormLoading(true);

    let cargosToSend = data.cargos;
    if (cargosToSend.includes('GESTOR')) {
      cargosToSend = ['GESTOR'];
    }

    const apiData: NovoMembroPayload = {
      nome_completo: data.nome,
      email: data.email,
      cpf: data.cpf,
      password: data.senha,
      cargos: cargosToSend,
    };

    try {
      await franquiaService.adicionarMembro(user.franquia_membership.franquia_id, apiData);
      alert('Funcionário criado com sucesso!');
      navigate('/funcionarios'); // ou para a lista de membros da franquia
    } catch (error) {
      console.error('Erro ao criar funcionário:', error);
      alert('Erro ao criar funcionário. Verifique os dados e tente novamente.');
    } finally {
      setFormLoading(false);
    }
  };

  if (authLoading) {
    return <div>Carregando informações do usuário...</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Cadastrar Novo Membro</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        {availableCargos.length > 0 ? (
          <FuncionarioForm
            onSubmit={handleSubmit}
            loading={formLoading}
            isEditing={false}
            availableCargos={availableCargos}
          />
        ) : (
          <div className="text-center text-gray-500 p-8">
            <p>Você não tem permissão para cadastrar novos funcionários.</p>
          </div>
        )}
      </div>
    </div>
  );
};