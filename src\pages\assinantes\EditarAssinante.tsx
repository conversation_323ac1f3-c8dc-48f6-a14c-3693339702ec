import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AssinanteForm } from './AssinanteForm';
import { assinanteService } from '../../services/assinanteService';
import { Assinante, AssinanteFormFields, AssinanteUpdate } from '../../types/Assinante';

export const EditarAssinante: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [assinante, setAssinante] = useState<Assinante | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchAssinante = async () => {
      if (id) {
        try {
          // Ponto de integração: Chama o serviço para buscar os dados do assinante pelo ID no backend.
          const data = await assinanteService.buscarPorId(Number(id));
          setAssinante(data);
        } catch (error) {
          console.error('Erro ao buscar assinante:', error);
          alert('Erro ao buscar assinante para edição.');
          navigate('/assinantes');
        } finally {
          setLoading(false);
        }
      }
    };
    fetchAssinante();
  }, [id, navigate]);

  const handleSubmit = async (data: AssinanteFormFields) => {
    if (id) {
      try {
        // Mapeia AssinanteFormFields para AssinanteUpdate
        const assinanteParaAtualizar: AssinanteUpdate = {
          nome_completo: data.nome_completo,
          email: data.email,
          bloqueado: data.bloqueado,
          // Não inclua 'password' aqui, a menos que seja para uma funcionalidade de mudança de senha
        };

        // Ponto de integração: Chama o serviço para atualizar o assinante no backend.
        // O backend deve ser capaz de lidar com a atualização parcial ou completa dos dados.
        await assinanteService.atualizar(Number(id), assinanteParaAtualizar); // Chama com AssinanteUpdate
        alert('Assinante atualizado com sucesso!');
        navigate('/assinantes');
      } catch (error) {
        console.error('Erro ao atualizar assinante:', error);
        alert('Erro ao atualizar assinante. Verifique o console para mais detalhes.');
      }
    }
  };

  if (loading) {
    return <div className="container mx-auto px-4 py-8 text-center">Carregando...</div>;
  }

  if (!assinante) {
    return <div className="container mx-auto px-4 py-8 text-center">Assinante não encontrado.</div>;
  }

  // Mapeia Assinante para AssinanteFormFields para initialData
  const initialFormValues: AssinanteFormFields | undefined = assinante ? {
    nome_completo: assinante.nome_completo,
    email: assinante.email,
    bloqueado: assinante.bloqueado,
    // Não inclua password aqui, pois não é retornado pela API ao buscar
  } : undefined;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Editar Assinante</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <AssinanteForm initialData={initialFormValues} onSubmit={handleSubmit} isEdit={true} />
      </div>
    </div>
  );
};
