import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Empreendimento, ParteEmpreendimento } from '../../types/empreendimento';
import { Incorporadora } from '../../types/incorporadora';
import { empreendimentoService } from '../../services/empreendimentoService';
import { incorporadoraService } from '../../services/incorporadoraService';
import EmpreendimentoEditForm from './EmpreendimentoEditForm';

// Serviços para dados locais

import { ItemEmpreendimentoService } from '../../services/empreendimentoService';

// Adicione esta função ao seu componente ou a um arquivo de utilitários
const getDisponibilidadeLabel = (codigo: number | undefined): string | undefined => {
  switch (codigo) {
    case 1:
      return "Disponível";
    case 2:
      return "Vendido";
    case 3:
      return "Distratado";
    default:
      return undefined;
  }
};


const itemEmpreendimentoService = new ItemEmpreendimentoService();

const VisualizarEmpreendimento: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [empreendimento, setEmpreendimento] = useState<Empreendimento | null>(null);
  const [incorporadora, setIncorporadora] = useState<Incorporadora | null>(null);
  const [partesEmpreendimento, setPartesEmpreendimento] = useState<ParteEmpreendimento[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  
  // Estados para dados específicos
  const [faseEmpreendimento, setFaseEmpreendimento] = useState<number | undefined>(undefined);
  const [tipoEmpreendimento, setTipoEmpreendimento] = useState<string | undefined>(undefined);
  const [itensEmpreendimento, setItensEmpreendimento] = useState<string[]>([]);

  useEffect(() => {
    if (id) {
      carregarEmpreendimento(parseInt(id));
    }
  }, [id]);

  const carregarEmpreendimento = async (empreendimentoId: number) => {
    try {
      setLoading(true);
      setError(null);

      if (!empreendimentoId || isNaN(empreendimentoId)) {
        throw new Error('ID do empreendimento inválido');
      }

      // Tentar buscar via getById primeiro
      let empreendimentoEncontrado: Empreendimento;
      try {
        empreendimentoEncontrado = await empreendimentoService.getById(empreendimentoId);
      } catch (getByIdError) {
        // Fallback para listagem
        const listResponse = await empreendimentoService.list({ limit: 1000 });
        empreendimentoEncontrado = listResponse.results.find(emp => emp.id === empreendimentoId)!;
      }
      
      if (!empreendimentoEncontrado) {
        throw new Error(`Empreendimento com ID ${empreendimentoId} não encontrado`);
      }

      setEmpreendimento(empreendimentoEncontrado);

      // Carregar dados da incorporadora
      if (empreendimentoEncontrado.incorporadora) {
        try {
          const incorporadoraData = await incorporadoraService.buscarPorId(empreendimentoEncontrado.incorporadora);
          setIncorporadora(incorporadoraData);
        } catch (err) {
          console.error('Erro ao carregar incorporadora:', err);
        }
      }

      // Carregar partes do empreendimento
      try {
        const partesData = await empreendimentoService.listPartesEmpreendimento(empreendimentoId);
        setPartesEmpreendimento(partesData);
      } catch (err) {
        console.error('Erro ao carregar partes do empreendimento:', err);
        setPartesEmpreendimento([]);
      }

      // Carregar dados específicos da API
      carregarDadosEspecificos(empreendimentoId);
      
      // Definir dados específicos a partir do empreendimento retornado pela API
      setFaseEmpreendimento(empreendimentoEncontrado.fase_atual);
      setTipoEmpreendimento(empreendimentoEncontrado.tipo_empreendimento_descricao);
      setItensEmpreendimento(empreendimentoEncontrado.itens_empreendimento_descricoes || []);
      
      console.log('=== DADOS ESPECÍFICOS DA API ===');
      console.log('Fase atual:', empreendimentoEncontrado.fase_atual);
      console.log('Tipo descrição:', empreendimentoEncontrado.tipo_empreendimento_descricao);
      console.log('Itens descrições:', empreendimentoEncontrado.itens_empreendimento_descricoes);

    } catch (err: any) {
      console.error('Erro ao carregar empreendimento:', err);
      setError(err.message || 'Erro ao carregar empreendimento');
    } finally {
      setLoading(false);
    }
  };

  const carregarDadosEspecificos = (empreendimentoId: number) => {
    try {
      console.log('=== CARREGANDO DADOS ESPECÍFICOS DA API ===');
      console.log('ID do empreendimento:', empreendimentoId);
      
      // Os dados específicos já estão no objeto empreendimento retornado pela API
      // Não precisamos mais carregar do localStorage
      console.log('Dados específicos carregados da API');
    } catch (error) {
      console.error('Erro ao carregar dados específicos:', error);
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleSave = async (updatedData: Partial<Empreendimento>) => {
    if (!empreendimento) return;

    try {
      setLoading(true);
      // Remover campos que não devem ser enviados na atualização
      const { endereco, ...dataToUpdate } = updatedData;
      const updatedEmpreendimento = await empreendimentoService.patch(empreendimento.id, dataToUpdate);
      setEmpreendimento(updatedEmpreendimento); 
      
      // Os dados específicos são atualizados diretamente na API
      // Não precisamos mais salvar no localStorage
      console.log('Dados específicos atualizados na API');
      
      setIsEditing(false);
    } catch (err: any) {
      console.error('Erro ao atualizar empreendimento:', err);
      setError(err.message || 'Erro ao atualizar empreendimento');
    } finally {
      setLoading(false);
    }
  };

  const formatarData = (data: string | undefined) => {
    if (!data) return '-';
    return new Date(data).toLocaleDateString('pt-BR');
  };

  const formatarMoeda = (valor: string | number | undefined) => {
    if (!valor) return '-';
    const num = typeof valor === 'string' ? parseFloat(valor) : valor;
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(num);
  };

  const formatarArea = (area: string | undefined) => {
    if (!area) return '-';
    const num = parseFloat(area);
    return `${num.toFixed(2)} m²`;
  };

  const formatarFaseEmpreendimento = (fase: number | undefined) => {
    if (!fase) return '-';
    const fases = {
      1: 'Planta',
      2: 'Fundação',
      3: 'Estrutura',
      4: 'Acabamento',
      5: 'Pronto'
    };
    return fases[fase as keyof typeof fases] || `Fase ${fase}`;
  };

  const formatarOrigemRecurso = (origem: number | undefined) => {
    if (!origem) return '-';
    const origens = {
      1: 'Próprio',
      2: 'SFH',
      3: 'Condomínio',
      4: 'Cooperativa',
      5: 'Próprio+SFH',
      6: 'OUTROS',
      7: 'MCMV'
    };
    return origens[origem as keyof typeof origens] || `Origem ${origem}`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Erro ao carregar empreendimento</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!empreendimento) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Empreendimento não encontrado</h2>
          <p className="mt-2 text-gray-600">O empreendimento solicitado não foi encontrado.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{empreendimento.nome}</h1>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <span className="text-lg text-gray-600">
                  {incorporadora?.empresa?.nome_fantasia || `Incorporadora ${empreendimento.incorporadora}`}
                </span>
              </div>
              {empreendimento.endereco?.logradouro?.cidade && (
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span className="text-lg text-gray-600">
                    {empreendimento.endereco.logradouro.cidade.nome} - {empreendimento.endereco.logradouro.cidade.uf}
                  </span>
                </div>
              )}
            </div>
          </div>
          <div className="flex space-x-3 ml-6">
            <button
              onClick={() => navigate('/empreendimentos')}
              className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                <span>Voltar</span>
              </div>
            </button>
            {!isEditing ? (
              <button
                onClick={handleEdit}
                className="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <span>Editar</span>
                </div>
              </button>
            ) : (
              <button
                onClick={handleCancelEdit}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  <span>Cancelar</span>
                </div>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Modo de Edição */}
      {isEditing ? (
        <EmpreendimentoEditForm
          empreendimento={empreendimento}
          onSave={handleSave}
          onCancel={handleCancelEdit}
          loading={loading}
        />
      ) : (
        <>
          {/* Informações Básicas */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 mb-8">
            <div className="px-6 py-5 border-b border-gray-100">
              <h3 className="text-xl font-semibold text-gray-900">Informações Básicas</h3>
              <p className="mt-1 text-sm text-gray-500">Detalhes fundamentais do empreendimento</p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Nome */}
                <div className="space-y-2">
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Nome do Empreendimento</label>
                  <p className="text-lg font-medium text-gray-900">{empreendimento.nome}</p>
                </div>

                {/* Incorporadora */}
                <div className="space-y-2">
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Incorporadora</label>
                  <p className="text-lg font-medium text-gray-900">
                    {incorporadora?.empresa?.nome_fantasia || `Incorporadora ${empreendimento.incorporadora}`}
                  </p>
                </div>

                {/* Área Total */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Área Total</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.area_total || '-'}
                    </p>
                  </div>

                  {/* Área Construída */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Área Construída</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.area_construida || '-'}
                    </p>
                  </div>

                  {/* É Lançamento */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">É Lançamento?</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.is_lancamento ? 'Sim' : 'Não'}
                    </p>
                  </div>

                {/* Data de Entrega */}
                <div className="space-y-2">
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Data de Entrega</label>
                  <p className="text-lg font-medium text-gray-900">
                    {formatarData(empreendimento.data_entrega)}
                  </p>
                </div>

                {/* Data de Início da Comercialização */}
                <div className="space-y-2">
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Data de Início da Comercialização</label>
                  <p className="text-lg font-medium text-gray-900">
                    {formatarData(empreendimento.data_inicio_comercializacao)}
                  </p>
                </div>

                {/* Observações */}
                <div className="space-y-2 md:col-span-2">
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Observações</label>
                  <p className="text-lg font-medium text-gray-900">
                    {empreendimento.observacao || '-'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Endereço */}
          {empreendimento.endereco && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 mb-8">
              <div className="px-6 py-5 border-b border-gray-100">
                <h3 className="text-xl font-semibold text-gray-900">Endereço</h3>
                <p className="mt-1 text-sm text-gray-500">Localização do empreendimento</p>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Logradouro */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Logradouro</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.endereco.logradouro?.nome}
                    </p>
                  </div>

                  {/* Número */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Número</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.endereco.numero}
                    </p>
                  </div>

                  {/* Complemento */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Complemento</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.endereco.complemento || '-'}
                    </p>
                  </div>

                  {/* Bairro */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Bairro</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.endereco.logradouro?.bairro}
                    </p>
                  </div>

                  {/* Cidade/UF */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Cidade/UF</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.endereco.logradouro?.cidade?.nome} - {empreendimento.endereco.logradouro?.cidade?.uf}
                    </p>
                  </div>

                  {/* CEP */}
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">CEP</label>
                    <p className="text-lg font-medium text-gray-900">
                      {empreendimento.endereco.logradouro?.zipcode}
                    </p>
                  </div>

                  {/* Coordenadas */}
                  {(empreendimento.endereco.latitude || empreendimento.endereco.longitude) && (
                    <div className="space-y-2 md:col-span-2">
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Coordenadas</label>
                      <p className="text-lg font-medium text-gray-900">
                        {empreendimento.endereco.latitude}, {empreendimento.endereco.longitude}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Informações Específicas */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="px-6 py-5 border-b border-gray-100">
              <h3 className="text-xl font-semibold text-gray-900">Informações Específicas</h3>
              <p className="mt-1 text-sm text-gray-500">Detalhes técnicos e comerciais</p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Fase do Empreendimento */}
                <div className="space-y-2">
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Fase do Empreendimento</label>
                  <p className="text-lg font-medium text-gray-900">
                    {faseEmpreendimento ? formatarFaseEmpreendimento(faseEmpreendimento) : 'Não informado'}
                  </p>
                </div>

                {/* Tipo do Empreendimento */}
                <div className="space-y-2">
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tipo do Empreendimento</label>
                  <p className="text-lg font-medium text-gray-900">
                    {tipoEmpreendimento || 'Não informado'}
                  </p>
                </div>

                {/* Itens do Empreendimento */}
                <div className="space-y-2 md:col-span-2">
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Itens do Empreendimento</label>
                  <p className="text-lg font-medium text-gray-900">
                    {itensEmpreendimento && itensEmpreendimento.length > 0
                      ? itensEmpreendimento.join(', ')
                      : 'Não informado'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Tipologias */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="px-6 py-5 border-b border-gray-100">
              <h3 className="text-xl font-semibold text-gray-900">Tipologias</h3>
              <p className="mt-1 text-sm text-gray-500">Unidades e tipos disponíveis</p>
            </div>
            <div className="p-6">
              {partesEmpreendimento.length > 0 ? (
                <div className="space-y-6">
                  {partesEmpreendimento.map((parte, index) => (
                    <div key={parte.id} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="text-lg font-semibold text-gray-900">{parte.nome}</h4>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Tipo {parte.tipo_parte_empreendimento}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Área Privativa */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Área Privativa</label>
                          <p className="text-sm font-medium text-gray-900">{formatarArea(parte.area_privativa_m2)}</p>
                        </div>

                        {/* Valor do Imóvel */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Valor do Imóvel</label>
                          <p className="text-sm font-medium text-gray-900">{formatarMoeda(parte.valor_imovel)}</p>
                        </div>

                     

                        {/* Forma de Pagamento */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Forma de Pagamento</label>
                          <p className="text-sm font-medium text-gray-900">{parte.forma_pagamento || '-'}</p>
                        </div>
                        {/* Disponibilidade */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Disponibilidade
                          </label>
                          <p className="text-sm font-medium text-gray-900">
                            {getDisponibilidadeLabel(parte.disponibilidade) || '-'}
                          </p>
                        </div>

                        {/* Quantidade de Garagem */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Quantidade de Garagem
                          </label>
                          <p className="text-sm font-medium text-gray-900">
                            {parte.quantidade_garagem || '-'}
                          </p>
                        </div>

                        {/* Origem do Recurso */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Origem do Recurso</label>
                          <p className="text-sm font-medium text-gray-900">{formatarOrigemRecurso(parte.origem_recurso)}</p>
                        </div>

                        {/* Foi Lançamento */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Foi Lançamento</label>
                          <p className="text-sm font-medium text-gray-900">
                            {parte.was_lancamento ? (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Sim
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Não
                              </span>
                            )}
                          </p>
                        </div>

                        {/* Ano de Disponibilidade */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Ano de Disponibilidade</label>
                          <p className="text-sm font-medium text-gray-900">{parte.ano_disponibilidade}</p>
                        </div>

                        {/* Mês de Disponibilidade */}
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Mês de Disponibilidade</label>
                          <p className="text-sm font-medium text-gray-900">{parte.mes_disponibilidade}</p>
                        </div>

                        {/* Atributos Extras */}
                        {parte.atributos_extras && Object.keys(parte.atributos_extras).length > 0 && (
                          <div className="space-y-1 md:col-span-3">
                            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Atributos Extras</label>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {Object.entries(parte.atributos_extras).map(([key, value]) => (
                                <div key={key} className="flex justify-between">
                                  <span className="text-sm text-gray-600">{key}:</span>
                                  <span className="text-sm font-medium text-gray-900">{String(value)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm text-gray-500">Nenhuma tipologia encontrada.</p>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default VisualizarEmpreendimento; 