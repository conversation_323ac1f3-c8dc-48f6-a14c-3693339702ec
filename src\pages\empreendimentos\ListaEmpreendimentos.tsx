import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { empreendimentoService } from '../../services/empreendimentoService';
import { incorporadoraService } from '../../services/incorporadoraService';
import { cidadeService } from '../../services/cidadeService';
import { Empreendimento } from '../../types/empreendimento';
import { Incorporadora } from '../../types/incorporadora';
import { Cidade } from '../../types/cidade';
import { PaginatedTable } from '../../components/Table/PaginatedTable';

/**
 * Listagem de Empreendimentos com Filtros Backend
 * 
 * Agora utiliza filtros implementados no backend para melhor performance
 * e escalabilidade.
 */
const ListaEmpreendimentos: React.FC = () => {
  const [empreendimentos, setEmpreendimentos] = useState<Empreendimento[]>([]);
  const [incorporadoras, setIncorporadoras] = useState<Incorporadora[]>([]);
  const [cidades, setCidades] = useState<Cidade[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [filters, setFilters] = useState({
    search: '',
    incorporadora: '',
    cidade: ''
  });

  // Buscar dados de referência (incorporadoras e cidades)
  const fetchReferenceData = async () => {
    try {
      console.log('=== CARREGANDO DADOS DE REFERÊNCIA ===');
      const [incorporadorasData, cidadesData] = await Promise.all([
        incorporadoraService.listarParaSelecao(),
        cidadeService.listar(1, 1000) 
      ]);
      
      console.log('Incorporadoras carregadas:', incorporadorasData);
      console.log('Cidades carregadas:', cidadesData);
      console.log('Estrutura das cidades:', cidadesData.results?.map(c => ({ id: c.id, nome: c.nome, uf: c.uf })));
      
      setIncorporadoras(incorporadorasData);
      setCidades(cidadesData.results || []);
      
      console.log('Dados de referência salvos no estado');
    } catch (error) {
      console.error('Erro ao buscar dados de referência:', error);
    }
  };

  // Função para obter nome da incorporadora
  const getIncorporadoraNome = (incorporadoraId: number) => {
    console.log('=== DEBUG INCORPORADORA ===');
    console.log('ID da incorporadora:', incorporadoraId);
    console.log('Incorporadoras disponíveis:', incorporadoras);
    
    const incorporadora = incorporadoras.find(inc => inc.id === incorporadoraId);
    console.log('Incorporadora encontrada:', incorporadora);
    console.log('Estrutura da incorporadora:', incorporadora ? JSON.stringify(incorporadora, null, 2) : 'null');
    
    // Usar empresa diretamente conforme nova documentação do backend
    const nome = incorporadora?.empresa?.nome_fantasia || `Incorporadora ${incorporadoraId}`;
    console.log('Nome retornado:', nome);
    return nome;
  };

  // Função para obter nome da cidade
  const getCidadeNome = (endereco: any) => {
    console.log('=== DEBUG CIDADE ===');
    console.log('Endereço recebido:', endereco);
    console.log('Tipo do endereço:', typeof endereco);
    console.log('Cidades disponíveis:', cidades.map(c => ({ id: c.id, nome: c.nome, uf: c.uf })));
    console.log('Quantidade de cidades carregadas:', cidades.length);
    
    if (!endereco) {
      console.log('Endereço é null/undefined');
      return 'Não informado';
    }
    
    if (typeof endereco === 'number') {
      console.log('Endereço é apenas ID:', endereco);
      return `ID: ${endereco}`;
    }
    
    console.log('Estrutura completa do endereço:', JSON.stringify(endereco, null, 2));
    console.log('Logradouro:', endereco.logradouro);
    console.log('Cidade no logradouro:', endereco.logradouro?.cidade);
    console.log('Tipo da cidade:', typeof endereco.logradouro?.cidade);
    
    if (endereco.logradouro?.cidade) {
      // Se cidade é um objeto completo
      if (typeof endereco.logradouro.cidade === 'object' && endereco.logradouro.cidade.nome) {
        console.log('Cidade é objeto completo:', endereco.logradouro.cidade);
        return `${endereco.logradouro.cidade.nome}/${endereco.logradouro.cidade.uf}`;
      }
      // Se cidade é apenas o ID
      console.log('Cidade é ID:', endereco.logradouro.cidade);
      const cidade = cidades.find(c => c.id === endereco.logradouro.cidade);
      console.log('Cidade encontrada no array:', cidade);
      return cidade ? `${cidade.nome}/${cidade.uf}` : `Cidade ID: ${endereco.logradouro.cidade}`;
    }
    
    console.log('Estrutura não reconhecida');
    return 'Endereço não disponível';
  };

  // Buscar empreendimentos com filtros do backend
  const fetchEmpreendimentos = async () => {
    try {
      setLoading(true);
      const params = {
        limit: pageSize,
        offset: (currentPage - 1) * pageSize,
        ...(filters.search && { search: filters.search }),
        ...(filters.incorporadora && { incorporadora: parseInt(filters.incorporadora) }),
        ...(filters.cidade && { cidade: parseInt(filters.cidade) })
      };

      console.log('Buscando empreendimentos com params:', params);
      const response = await empreendimentoService.list(params);
      console.log('Resposta da API:', response);
      
      setEmpreendimentos(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Erro ao buscar empreendimentos:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReferenceData();
  }, []);

  useEffect(() => {
    fetchEmpreendimentos();
  }, [currentPage, pageSize, filters]); // Agora refaz a busca quando filtros mudam

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setCurrentPage(1); // Reset para primeira página ao filtrar
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Tem certeza que deseja excluir este empreendimento?')) {
      try {
        await empreendimentoService.delete(id);
        // Recarregar dados após exclusão
        fetchEmpreendimentos();
      } catch (error) {
        console.error('Erro ao excluir empreendimento:', error);
        alert('Erro ao excluir empreendimento');
      }
    }
  };

  const columns = [
    {
      key: 'incorporadora' as keyof Empreendimento,
      title: 'Incorporadora',
      render: (value: any, empreendimento: Empreendimento) => (
        <span className="font-medium text-gray-900">
          {getIncorporadoraNome(empreendimento.incorporadora)}
        </span>
      )
    },
    {
      key: 'nome' as keyof Empreendimento,
      title: 'Nome do Empreendimento',
      render: (value: any, empreendimento: Empreendimento) => (
        <Link 
          to={`/empreendimentos/${empreendimento.id}/visualizar`}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          {empreendimento.nome}
        </Link>
      )
    },
    {
      key: 'endereco' as keyof Empreendimento,
      title: 'Cidade',
      render: (value: any, empreendimento: Empreendimento) => (
        <span className="text-gray-700">
          {getCidadeNome(empreendimento.endereco)}
        </span>
      )
    },
    {
      key: 'data_entrega' as keyof Empreendimento,
      title: 'Data de Entrega',
      render: (value: any, empreendimento: Empreendimento) => (
        <span>
          {empreendimento.data_entrega 
            ? new Date(empreendimento.data_entrega).toLocaleDateString('pt-BR')
            : 'Não informado'
          }
        </span>
      )
    },
    {
      key: 'id' as keyof Empreendimento,
      title: 'Ações',
      render: (value: any, empreendimento: Empreendimento) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleDelete(empreendimento.id)}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            Excluir
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Empreendimentos</h1>
        <Link
          to="/empreendimentos/novo"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Novo Empreendimento
        </Link>
      </div>

      {/* Filtros */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-lg font-semibold mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome do Empreendimento
            </label>
            <input
              type="text"
              placeholder="Digite o nome do empreendimento..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Incorporadora
            </label>
            <select
              value={filters.incorporadora}
              onChange={(e) => handleFilterChange('incorporadora', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas as incorporadoras</option>
              {incorporadoras.map((incorporadora) => (
                <option key={incorporadora.id} value={incorporadora.id}>
                  {incorporadora.empresa?.nome_fantasia || `Incorporadora ${incorporadora.id}`}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cidade
            </label>
            <select
              value={filters.cidade}
              onChange={(e) => handleFilterChange('cidade', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas as cidades</option>
              {cidades.map((cidade) => (
                <option key={cidade.id} value={cidade.id}>
                  {cidade.nome}/{cidade.uf}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        {/* Botão para limpar filtros */}
        <div className="mt-4">
          <button
            onClick={() => setFilters({ search: '', incorporadora: '', cidade: '' })}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Limpar Filtros
          </button>
        </div>
      </div>

      {/* Tabela */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <PaginatedTable
          data={empreendimentos}
          columns={columns as any}
          loading={loading}
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / pageSize)}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  );
};
export default ListaEmpreendimentos; 