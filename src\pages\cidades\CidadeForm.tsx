import React from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { cidadeService } from '../../services/cidadeService';
import { CidadeInput } from '../../types/cidade';

interface CidadeFormProps {
  initialData?: CidadeInput;
  isEditing?: boolean;
}

export const CidadeForm: React.FC<CidadeFormProps> = ({ initialData, isEditing }) => {
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<CidadeInput>({
    defaultValues: initialData || {
      nome: '',
      estado: '',
      uf: '',
      pais: 'Brasil'
    }
  });

  const navigate = useNavigate();
  const { id } = useParams();

  const onSubmit = async (data: CidadeInput) => {
    try {
      if (isEditing && id) {
        await cidadeService.atualizar(Number(id), data);
        toast.success('Cidade atualizada com sucesso!');
      } else {
        await cidadeService.criar(data);
        toast.success('Cidade criada com sucesso!');
      }
      navigate('/cidades');
    } catch (error) {
      toast.error('Erro ao salvar cidade. Por favor, tente novamente.');
      console.error('Erro ao salvar cidade:', error);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <label htmlFor="nome" className="block text-sm font-medium text-gray-700">
            Nome
          </label>
          <input
            type="text"
            id="nome"
            {...register('nome', { required: 'Nome é obrigatório' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.nome && (
            <p className="mt-1 text-sm text-red-600">{errors.nome.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="estado" className="block text-sm font-medium text-gray-700">
            Estado
          </label>
          <input
            type="text"
            id="estado"
            {...register('estado', { required: 'Estado é obrigatório' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.estado && (
            <p className="mt-1 text-sm text-red-600">{errors.estado.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="uf" className="block text-sm font-medium text-gray-700">
            UF
          </label>
          <input
            type="text"
            id="uf"
            {...register('uf', { 
              required: 'UF é obrigatória',
              maxLength: { value: 2, message: 'UF deve ter 2 caracteres' },
              minLength: { value: 2, message: 'UF deve ter 2 caracteres' }
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.uf && (
            <p className="mt-1 text-sm text-red-600">{errors.uf.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="pais" className="block text-sm font-medium text-gray-700">
            País
          </label>
          <input
            type="text"
            id="pais"
            {...register('pais', { required: 'País é obrigatório' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.pais && (
            <p className="mt-1 text-sm text-red-600">{errors.pais.message}</p>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/cidades')}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmitting ? 'Salvando...' : isEditing ? 'Atualizar' : 'Criar'}
          </button>
        </div>
      </form>
    </div>
  );
}; 