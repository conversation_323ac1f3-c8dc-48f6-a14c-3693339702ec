export interface NovoMembroPayload {
  email: string;
  nome_completo: string;
  password: string;
  cpf: string;
  cargos: string[];
}

export interface MembroUser {
  id: number;
  nome_completo: string;
  email: string;
  cpf: string;
  conta_ativa: boolean;
  is_bloqueado: boolean;
  last_login: string | null;
  created: string;
}

export interface MembroFranquia {
  user: MembroUser;
  cargos: string[];
  status_na_franquia: string;
  excluido_em: string | null;
  created: string;
  modified: string;
}

export interface PaginatedMembrosFranquia {
  count: number;
  next: string | null;
  previous: string | null;
  results: MembroFranquia[];
}