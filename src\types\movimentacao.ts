export interface Movimentacao {
  id: number;
  incorporadora: Incorporadora;
  empreendimento: Empreendimento;
  tipo_movimentacao: TipoMovimentacaoEnum;
  quantidade?: number;
  ano_movimentacao?: number;
  mes_movimentacao?: MesMovimentacaoEnum;
  endereco: Endereco;
  padrao_acabamento: PadraoAcabamentoEnum;
  parte_empreendimento: ParteEmpreendimento;
  valor_m2: string;
  fase_empreendimento: FaseEmpreendimentoEnum;
  origem_recurso: OrigemRecursoEnum;
  motivo_distrato?: MotivoDistratoEnum;
  was_lancamento: boolean;
  created: string;
  modified: string;
}

export interface MovimentacaoCreate {
  tipo_movimentacao: TipoMovimentacaoEnum;
  quantidade?: number;
  ano_movimentacao?: number;
  mes_movimentacao?: MesMovimentacaoEnum;
  origem_recurso: OrigemRecursoEnum;
  motivo_distrato?: MotivoDistratoEnum;
  was_lancamento: boolean;

  incorporadora?: {
    empresa: number;
  };
  empreendimento?: {
    endereco: number;
    fase_empreendimento: number;
    tipo_empreendimento: string;
    item_empreendimento: string[];
    nome: string;
    quantidade_garagens: number;
    data_entrega: string;
    data_inicio_comercializacao: string;
    observacao: string;
    heading: string;
    pitch: string;
    incorporadora: number;
  };
  endereco?: {
    logradouro: number;
    numero: number;
    complemento: string;
    ponto_referencia: string;
    latitude: string;
    longitude: string;
  };
  parte_empreendimento?: {
    estoque_inicial: number;
    was_lancamento: boolean;
    ano_disponibilidade: number;
    mes_disponibilidade: number;
    origem_recurso: OrigemRecursoEnum;
    tipo_parte_empreendimento: number;
    nome: string;
    area_privativa_m2: string;
    valor_imovel: string;
    forma_pagamento: string;
    atributos_extras: string;
  };
}


export type TipoMovimentacaoEnum = 1 | 2 | 3 | 4;

export const TIPO_MOVIMENTACAO_OPTIONS = [
  { value: 1, label: 'Oferta' },
  { value: 2, label: 'Venda' },
  { value: 3, label: 'Distrato' },
  { value: 4, label: 'Avulso' }
] as const;

export type MesMovimentacaoEnum = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;

export const MES_MOVIMENTACAO_OPTIONS = [
  { value: 1, label: 'Janeiro' },
  { value: 2, label: 'Fevereiro' },
  { value: 3, label: 'Março' },
  { value: 4, label: 'Abril' },
  { value: 5, label: 'Maio' },
  { value: 6, label: 'Junho' },
  { value: 7, label: 'Julho' },
  { value: 8, label: 'Agosto' },
  { value: 9, label: 'Setembro' },
  { value: 10, label: 'Outubro' },
  { value: 11, label: 'Novembro' },
  { value: 12, label: 'Dezembro' }
] as const;

export type OrigemRecursoEnum = 1 | 2 | 3 | 4 | 5 | 6 | 7;

export const ORIGEM_RECURSO_OPTIONS = [
  { value: 1, label: 'Próprio' },
  { value: 2, label: 'SFH' },
  { value: 3, label: 'Condomínio' },
  { value: 4, label: 'Cooperativa' },
  { value: 5, label: 'Próprio+SFH' },
  { value: 6, label: 'OUTROS' },
  { value: 7, label: 'MCMV' }
] as const;

export type MotivoDistratoEnum = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

export const MOTIVO_DISTRATO_OPTIONS = [
  { value: 1, label: 'Troca de unidade' },
  { value: 2, label: 'Negativa de crédito' },
  { value: 3, label: 'Troca de empreendimento' },
  { value: 4, label: 'Diminuição de Renda' },
  { value: 5, label: 'Transferência de cidade' },
  { value: 6, label: 'Problema Familiar' },
  { value: 7, label: 'Perda de Interesse' },
  { value: 8, label: 'Desemprego' },
  { value: 9, label: 'Outros' }
] as const;

export type PadraoAcabamentoEnum = 1 | 2 | 3;

export const PADRAO_ACABAMENTO_OPTIONS = [
  { value: 1, label: 'Alto' },
  { value: 2, label: 'Médio' },
  { value: 3, label: 'Baixo' }
] as const;

export type FaseEmpreendimentoEnum = 1 | 2 | 3 | 4 | 5;

export const FASE_EMPREENDIMENTO_OPTIONS = [
  { value: 1, label: 'Planta' },
  { value: 2, label: 'Fundação' },
  { value: 3, label: 'Estrutura' },
  { value: 4, label: 'Acabamento' },
  { value: 5, label: 'Pronto' }
] as const;

// Interfaces auxiliares (simplificadas para o contexto)
export interface Incorporadora {
  id: number;
  empresa: number;
  created: string;
  modified: string;
}

export interface Empreendimento {
  id: number;
  nome: string;
  endereco: Endereco;
  incorporadora: number;
  created: string;
  modified: string;
}

export interface Endereco {
  id: number;
  logradouro: Logradouro;
  numero?: number;
  complemento?: string;
  ponto_referencia?: string;
  latitude: string;
  longitude: string;
  created: string;
  modified: string;
}

export interface Logradouro {
  id: number;
  nome: string;
  bairro: string;
  zipcode: string;
  cidade: Cidade;
  created: string;
  modified: string;
}

export interface Cidade {
  id: number;
  nome: string;
  uf: string;
  estado: string;
  created: string;
  modified: string;
}

export interface ParteEmpreendimento {
  id: number;
  nome: string;
  area_privativa_m2: string;
  valor_imovel: string;
  forma_pagamento: string;
  empreendimento: number;
  created: string;
  modified: string;
} 