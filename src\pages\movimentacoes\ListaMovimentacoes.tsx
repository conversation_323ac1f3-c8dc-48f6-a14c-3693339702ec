import React from 'react';
import { Link } from 'react-router-dom';
import { movimentacaoService } from '../../services/movimentacaoService';
import { Movimentacao } from '../../types/movimentacao';
import { PaginatedTable } from '../../components/Table/PaginatedTable';
import { usePagination } from '../../hooks/usePagination';

const ListaMovimentacoes: React.FC = () => {
  const { 
    data: movimentacoes, 
    loading, 
    error, 
    currentPage, 
    totalPages, 
    setPage 
  } = usePagination(movimentacaoService);



  const getTipoMovimentacaoLabel = (tipo: number) => {
    const tipos = {
      1: 'Oferta',
      2: 'Venda',
      3: 'Distrato',
      4: 'Avulso'
    };
    return tipos[tipo as keyof typeof tipos] || 'Desconhecido';
  };

  const getFaseEmpreendimentoLabel = (fase: number) => {
    const fases = {
      1: 'Planta',
      2: 'Fundação',
      3: 'Estrutura',
      4: 'Acabamento',
      5: 'Pronto'
    };
    return fases[fase as keyof typeof fases] || 'Desconhecida';
  };

  const getOrigemRecursoLabel = (origem: number) => {
    const origens = {
      1: 'Próprio',
      2: 'SFH',
      3: 'Condomínio',
      4: 'Cooperativa',
      5: 'Próprio+SFH',
      6: 'OUTROS',
      7: 'MCMV'
    };
    return origens[origem as keyof typeof origens] || 'Desconhecida';
  };

  const columns = [
    {
      key: 'id' as keyof Movimentacao,
      title: 'ID',
      render: (value: any, movimentacao: Movimentacao) => movimentacao.id
    },
    {
      key: 'empreendimento' as keyof Movimentacao,
      title: 'Empreendimento',
      render: (value: any, movimentacao: Movimentacao) => movimentacao.empreendimento.nome
    },
    {
      key: 'tipo_movimentacao' as keyof Movimentacao,
      title: 'Tipo',
      render: (value: any, movimentacao: Movimentacao) => getTipoMovimentacaoLabel(movimentacao.tipo_movimentacao)
    },
    {
      key: 'quantidade' as keyof Movimentacao,
      title: 'Quantidade',
      render: (value: any, movimentacao: Movimentacao) => movimentacao.quantidade || '-'
    },
    {
      key: 'fase_empreendimento' as keyof Movimentacao,
      title: 'Fase',
      render: (value: any, movimentacao: Movimentacao) => getFaseEmpreendimentoLabel(movimentacao.fase_empreendimento)
    },
    {
      key: 'origem_recurso' as keyof Movimentacao,
      title: 'Origem Recurso',
      render: (value: any, movimentacao: Movimentacao) => getOrigemRecursoLabel(movimentacao.origem_recurso)
    },
    {
      key: 'valor_m2' as keyof Movimentacao,
      title: 'Valor/m²',
      render: (value: any, movimentacao: Movimentacao) => 
        movimentacao.valor_m2 ? `R$ ${parseFloat(movimentacao.valor_m2).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : '-'
    },
    {
      key: 'was_lancamento' as keyof Movimentacao,
      title: 'Lançamento',
      render: (value: any, movimentacao: Movimentacao) => movimentacao.was_lancamento ? 'Sim' : 'Não'
    },
    {
      key: 'created' as keyof Movimentacao,
      title: 'Data Criação',
      render: (value: any, movimentacao: Movimentacao) => new Date(movimentacao.created).toLocaleDateString('pt-BR')
    }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <strong className="font-bold">Erro:</strong>
        <span className="block sm:inline"> {error.message}</span>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Movimentações</h1>
        <Link
          to="/movimentacoes/nova"
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Nova Movimentação
        </Link>
      </div>

      <PaginatedTable<Movimentacao>
        data={movimentacoes as Movimentacao[]}
        columns={columns}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setPage}
        loading={loading}
      />
    </div>
  );
};

export default ListaMovimentacoes; 