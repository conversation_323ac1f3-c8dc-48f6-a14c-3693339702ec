import api from './api';
import { Telefone, TelefoneInput, TelefoneUpdateInput } from '../types/telefone';

class TelefoneService {
  async listar(usuarioId?: number): Promise<{ results: Telefone[]; count: number }> {
    const params = usuarioId ? { usuario: usuarioId } : {};
    const response = await api.get('/telefones/', { params });
    return response.data;
  }

  async buscarPorId(id: number): Promise<Telefone> {
    const response = await api.get(`/telefones/${id}/`);
    return response.data;
  }

  async criar(data: TelefoneInput): Promise<Telefone> {
    const response = await api.post('/telefones/', data);
    return response.data;
  }

  async atualizar(id: number, data: TelefoneInput): Promise<Telefone> {
    const response = await api.put(`/telefones/${id}/`, data);
    return response.data;
  }

  async atualizarParcial(id: number, data: TelefoneUpdateInput): Promise<Telefone> {
    const response = await api.patch(`/telefones/${id}/`, data);
    return response.data;
  }

  async excluir(id: number): Promise<void> {
    await api.delete(`/telefones/${id}/`);
  }
}

export const telefoneService = new TelefoneService(); 