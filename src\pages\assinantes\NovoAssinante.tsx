import React from 'react';
import { AssinanteForm } from './AssinanteForm';
import { assinanteService } from '../../services/assinanteService';
import { AssinanteCreate, AssinanteFormFields } from '../../types/Assinante';
import { useNavigate } from 'react-router-dom';

export const NovoAssinante: React.FC = () => {
  const navigate = useNavigate();

  const handleSubmit = async (data: AssinanteFormFields) => {
    try {
      // Garante que 'password' está presente para AssinanteCreate
      if (!data.password) {
        alert('A senha é obrigatória para criar um novo assinante.');
        return; // Interrompe a execução se a senha não estiver presente
      }

      // Mapeia AssinanteFormFields para AssinanteCreate
      const assinanteParaCriar: AssinanteCreate = {
        nome_completo: data.nome_completo,
        email: data.email,
        password: data.password,
      };

      // Ponto de integração: Chama o serviço para criar um novo assinante no backend.
      // Certifique-se de que o backend está configurado para receber os dados de AssinanteCreate.
      await assinanteService.criar(assinanteParaCriar); // Chama com AssinanteCreate
      alert('Assinante cadastrado com sucesso!');
      navigate('/assinantes'); // Redireciona para a página de lista após o sucesso
    } catch (error) {
      console.error('Erro ao cadastrar assinante:', error);
      alert('Erro ao cadastrar assinante. Verifique o console para mais detalhes.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Novo Assinante</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <AssinanteForm onSubmit={handleSubmit} />
      </div>
    </div>
  );
};
