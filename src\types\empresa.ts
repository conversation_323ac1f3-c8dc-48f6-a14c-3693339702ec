export interface Endereco {
  id: number;
  logradouro: Logradouro;
  numero?: number;
  complemento?: string;
  ponto_referencia?: string;
  latitude: string;
  longitude: string;
  created: string;
  modified: string;
}

export interface Logradouro {
  id: number;
  cidade: Cidade;
  zipcode: string;
  nome: string;
  bairro: string;
  created: string;
  modified: string;
}

export interface Cidade {
  id: number;
  nome: string;
  estado: string;
  uf: string;
  pais: string;
  created: string;
  modified: string;
}

export interface Empresa {
  id: number;
  endereco: Endereco;
  razao_social: string;
  nome_fantasia: string;
  cnpj: string;
  created: string;
  modified: string;
}

export interface EmpresaInput {
  endereco: number; // ID do endereço
  razao_social: string;
  nome_fantasia: string;
  cnpj: string;
}

export interface EmpresaUpdateInput extends Partial<EmpresaInput> {}

export interface PaginatedEmpresaResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Empresa[];
} 