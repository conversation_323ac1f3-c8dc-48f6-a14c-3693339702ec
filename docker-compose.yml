version: '3.8'

services:
  # Development service with hot-reload
  web-dev:
    build:
      context: .
      target: build
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:8000/api
      - PORT=3001
    command: npm start

  # Production service
  web-prod:
    build: .
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://api.seu-dominio.com/api
    restart: unless-stopped 