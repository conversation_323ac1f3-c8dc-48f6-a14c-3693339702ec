import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { franqueadoService } from '../../services/franqueadoService';
import { FranqueadoInput, FranqueadoUpdateInput } from '../../types/franqueado';

interface FranqueadoFormProps {
  franqueado?: FranqueadoInput;
  isEditing?: boolean;
  franqueadoId?: number;
}

export const FranqueadoForm: React.FC<FranqueadoFormProps> = ({ 
  franqueado, 
  isEditing = false, 
  franqueadoId 
}) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FranqueadoInput>({
    nome: franqueado?.nome || '',
    email: franqueado?.email || '',
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (franqueado) {
      setFormData({
        nome: franqueado.nome,
        email: franqueado.email,
      });
    }
  }, [franqueado]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (isEditing && franqueadoId) {
        await franqueadoService.atualizar(franqueadoId, formData);
      } else {
        await franqueadoService.criar(formData);
      }
      navigate('/franqueados');
    } catch (err: any) {
      console.error('Erro ao salvar franqueado:', err);
      setError(err.response?.data?.message || 'Erro ao salvar franqueado');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="md:grid md:grid-cols-3 md:gap-6">
        <div className="mt-5 md:mt-0 md:col-span-3">
          <form onSubmit={handleSubmit}>
            <div className="shadow sm:rounded-md sm:overflow-hidden">
              <div className="px-4 py-5 bg-white space-y-6 sm:p-6">
                {error && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">{error}</h3>
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <label htmlFor="nome" className="block text-sm font-medium text-[#29306a]">
                    Nome *
                  </label>
                  <input
                    type="text"
                    name="nome"
                    id="nome"
                    required
                    maxLength={255}
                    value={formData.nome}
                    onChange={handleInputChange}
                    className="mt-1 focus:ring-[#32bef0] focus:border-[#32bef0] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-[#29306a]">
                    Email *
                  </label>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    required
                    maxLength={254}
                    value={formData.email}
                    onChange={handleInputChange}
                    className="mt-1 focus:ring-[#32bef0] focus:border-[#32bef0] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                <button
                  type="button"
                  onClick={() => navigate('/franqueados')}
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-[#29306a] hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0] mr-3"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#32bef0] hover:bg-[#2ba8d8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#32bef0] disabled:opacity-50"
                >
                  {loading ? 'Salvando...' : (isEditing ? 'Atualizar' : 'Salvar')}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}; 