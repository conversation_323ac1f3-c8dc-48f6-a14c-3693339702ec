import React from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { areaCidadeService } from '../../services/areaCidadeService';
import { AreaCidadeInput } from '../../types/areaCidade';

interface AreaCidadeFormProps {
  initialData?: AreaCidadeInput;
  isEditing?: boolean;
}

export const AreaCidadeForm: React.FC<AreaCidadeFormProps> = ({ initialData, isEditing }) => {
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<AreaCidadeInput>({
    defaultValues: initialData || {
      nome: '',
      cidade: 0,
      franqueado_id: 0
    }
  });

  const navigate = useNavigate();
  const { id } = useParams();

  const onSubmit = async (data: AreaCidadeInput) => {
    try {
      if (isEditing && id) {
        await areaCidadeService.atualizar(Number(id), data);
        toast.success('Área-cidade atualizada com sucesso!');
      } else {
        await areaCidadeService.criar(data);
        toast.success('Área-cidade criada com sucesso!');
      }
      navigate('/areas-cidade');
    } catch (error) {
      toast.error('Erro ao salvar área-cidade. Por favor, tente novamente.');
      console.error('Erro ao salvar área-cidade:', error);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditing ? 'Editar Área-Cidade' : 'Nova Área-Cidade'}
        </h1>
        <p className="mt-2 text-sm text-gray-700">
          {isEditing ? 'Atualize os dados da área-cidade' : 'Preencha os dados para criar uma nova área-cidade'}
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <label htmlFor="nome" className="block text-sm font-medium text-gray-700">
            Nome
          </label>
          <input
            type="text"
            id="nome"
            {...register('nome', { required: 'Nome é obrigatório' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.nome && (
            <p className="mt-1 text-sm text-red-600">{errors.nome.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="cidade" className="block text-sm font-medium text-gray-700">
            Cidade ID
          </label>
          <input
            type="number"
            id="cidade"
            {...register('cidade', { 
              required: 'Cidade é obrigatória',
              min: { value: 1, message: 'Cidade ID deve ser maior que 0' }
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.cidade && (
            <p className="mt-1 text-sm text-red-600">{errors.cidade.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="franqueado_id" className="block text-sm font-medium text-gray-700">
            Franqueado ID
          </label>
          <input
            type="number"
            id="franqueado_id"
            {...register('franqueado_id', { 
              required: 'Franqueado é obrigatório',
              min: { value: 1, message: 'Franqueado ID deve ser maior que 0' }
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {errors.franqueado_id && (
            <p className="mt-1 text-sm text-red-600">{errors.franqueado_id.message}</p>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/areas-cidade')}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmitting ? 'Salvando...' : isEditing ? 'Atualizar' : 'Criar'}
          </button>
        </div>
      </form>
    </div>
  );
}; 