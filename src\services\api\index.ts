import { CrudService } from '../crud';
import {
  Assinatura,
  Cidade,
  Empreendimento,
  Franqueado,
  Incorporadora,
  Plano,
} from '../../types/api';

export const assinaturaService = new CrudService<Assinatura>('/assinaturas/');
export const cidadeService = new CrudService<Cidade>('/cidades/');
export const empreendimentoService = new CrudService<Empreendimento>('/empreendimentos/');
export const franqueadoService = new CrudService<Franqueado>('/franqueados/');
export const incorporadoraService = new CrudService<Incorporadora>('/incorporadoras/');
export const planoService = new CrudService<Plano>('/planos/'); 