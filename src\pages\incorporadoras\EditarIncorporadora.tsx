import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { incorporadoraService } from '../../services/incorporadoraService';
import { IncorporadoraForm } from './IncorporadoraForm';
import { Incorporadora } from '../../types/incorporadora';

export const EditarIncorporadora: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [incorporadora, setIncorporadora] = useState<Incorporadora | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchIncorporadora = async () => {
      if (!id) {
        setError('ID da incorporadora não fornecido');
        setLoading(false);
        return;
      }

      try {
        const incorporadoraData = await incorporadoraService.buscarPorId(parseInt(id));
        setIncorporadora(incorporadoraData);
      } catch (err) {
        console.error('Erro ao carregar incorporadora:', err);
        setError('Erro ao carregar dados da incorporadora');
      } finally {
        setLoading(false);
      }
    };

    fetchIncorporadora();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#32bef0]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">{error}</h3>
                <button
                  onClick={() => navigate('/incorporadoras')}
                  className="mt-2 text-sm text-red-600 hover:text-red-500"
                >
                  Voltar para lista
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!incorporadora) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="rounded-md bg-yellow-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Incorporadora não encontrada
                </h3>
                <button
                  onClick={() => navigate('/incorporadoras')}
                  className="mt-2 text-sm text-yellow-600 hover:text-yellow-500"
                >
                  Voltar para lista
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-[#29306a]">Editar Incorporadora</h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Atualize as informações da incorporadora
          </p>
        </div>
        <IncorporadoraForm 
          incorporadora={incorporadora} 
          isEditing={true} 
          incorporadoraId={parseInt(id!)} 
        />
      </div>
    </div>
  );
}; 