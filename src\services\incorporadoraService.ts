import { CrudService } from './crud';
import { Incorporadora, IncorporadoraInput, IncorporadoraUpdateInput, PaginatedIncorporadoraResponse } from '../types/incorporadora';
import api from './api';

class IncorporadoraService extends CrudService<Incorporadora> {
  constructor() {
    super('/incorporadoras/');
  }

  async criar(incorporadora: IncorporadoraInput): Promise<Incorporadora> {
    const response = await api.post<Incorporadora>('/incorporadoras/', incorporadora);
    return response.data;
  }

  async buscarPorId(id: number): Promise<Incorporadora> {
    return this.get(id);
  }

  async atualizar(id: number, incorporadora: IncorporadoraUpdateInput): Promise<Incorporadora> {
    const response = await api.put<Incorporadora>(`/incorporadoras/${id}/`, incorporadora);
    return response.data;
  }

  async atualizarParcial(id: number, incorporadora: IncorporadoraUpdateInput): Promise<Incorporadora> {
    const response = await api.patch<Incorporadora>(`/incorporadoras/${id}/`, incorporadora);
    return response.data;
  }

  async excluir(id: number): Promise<void> {
    return this.delete(id);
  }

  async listar(page: number = 1, limit: number = 10): Promise<PaginatedIncorporadoraResponse> {
    const offset = (page - 1) * limit;
    const response = await api.get<PaginatedIncorporadoraResponse>(`/incorporadoras/?offset=${offset}&limit=${limit}`);
    return response.data;
  }

  async listarParaSelecao(): Promise<Incorporadora[]> {
    const response = await api.get<PaginatedIncorporadoraResponse>('/incorporadoras/?limit=1000');
    return response.data.results;
  }
}

export const incorporadoraService = new IncorporadoraService(); 