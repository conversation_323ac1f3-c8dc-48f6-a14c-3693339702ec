import { CrudService } from './crud';
import { Empresa, EmpresaInput, PaginatedEmpresaResponse } from '../types/incorporadora';
import api from './api';

class EmpresaService extends CrudService<Empresa> {
  constructor() {
    super('/empresas/');
  }

  async criar(empresa: EmpresaInput): Promise<Empresa> {
    const response = await api.post<Empresa>('/empresas/', empresa);
    return response.data;
  }

  async buscarPorId(id: number): Promise<Empresa> {
    return this.get(id);
  }

  async atualizar(id: number, empresa: EmpresaInput): Promise<Empresa> {
    const response = await api.put<Empresa>(`/empresas/${id}/`, empresa);
    return response.data;
  }

  async atualizarParcial(id: number, empresa: Partial<EmpresaInput>): Promise<Empresa> {
    const response = await api.patch<Empresa>(`/empresas/${id}/`, empresa);
    return response.data;
  }

  async excluir(id: number): Promise<void> {
    return this.delete(id);
  }

  // Listar todas as empresas (com paginação)
  async listar(page: number = 1, limit: number = 10): Promise<PaginatedEmpresaResponse> {
    const offset = (page - 1) * limit;
    const response = await api.get<PaginatedEmpresaResponse>(`/empresas/?offset=${offset}&limit=${limit}`);
    return response.data;
  }

  async listarParaSelecao(): Promise<Empresa[]> {
    const response = await api.get<PaginatedEmpresaResponse>('/empresas/?limit=1000');
    return response.data.results;
  }
}

export const empresaService = new EmpresaService(); 