import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { incorporadoraService } from '../../services/incorporadoraService';
import { empresaService } from '../../services/empresaService';
import { enderecoService } from '../../services/enderecoService';
import { cidadeService } from '../../services/cidadeService';
import { Incorporadora, EmpresaInput } from '../../types/incorporadora';
import { EnderecoCreate } from '../../services/enderecoService';

interface IncorporadoraFormProps {
  incorporadora?: Incorporadora;
  isEditing?: boolean;
  incorporadoraId?: number;
}

export const IncorporadoraForm: React.FC<IncorporadoraFormProps> = ({ 
  incorporadora, 
  isEditing = false, 
  incorporadoraId 
}) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<EmpresaInput>({
    endereco: incorporadora?.empresa?.endereco?.id || 0,
    razao_social: incorporadora?.empresa?.razao_social || '',
    nome_fantasia: incorporadora?.empresa?.nome_fantasia || '',
    cnpj: incorporadora?.empresa?.cnpj || ''
  });

  // Tipo específico para o estado do endereço no formulário
  interface EnderecoFormData {
    logradouro: {
      cidade: number;
      zipcode: string;
      nome: string;
      bairro: string;
    };
    numero?: number;
    complemento: string;
    ponto_referencia: string;
    latitude: string;
    longitude: string;
  }

  const [enderecoData, setEnderecoData] = useState<EnderecoFormData>({
    logradouro: {
      cidade: 0,
      zipcode: '',
      nome: '',
      bairro: ''
    },
    numero: undefined,
    complemento: '',
    ponto_referencia: '',
    latitude: '',
    longitude: ''
  });

  const [cidades, setCidades] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingCidades, setLoadingCidades] = useState(true);

  useEffect(() => {
    const fetchCidades = async () => {
      try {
        const response = await cidadeService.listar(1, 1000); // Buscar todas as cidades
        setCidades(response.results || []);
      } catch (err) {
        console.error('Erro ao carregar cidades:', err);
        setError('Erro ao carregar lista de cidades');
      } finally {
        setLoadingCidades(false);
      }
    };

    fetchCidades();
  }, []);

  useEffect(() => {
    if (incorporadora?.empresa) {
      setFormData({
        endereco: incorporadora.empresa?.endereco?.id || 0,
        razao_social: incorporadora.empresa?.razao_social || '',
        nome_fantasia: incorporadora.empresa?.nome_fantasia || '',
        cnpj: incorporadora.empresa?.cnpj || ''
      });

      // Converter endereço para o formato do formulário
      setEnderecoData({
        logradouro: {
          cidade: incorporadora.empresa.endereco.logradouro.cidade.id,
          zipcode: incorporadora.empresa.endereco.logradouro.zipcode,
          nome: incorporadora.empresa.endereco.logradouro.nome,
          bairro: incorporadora.empresa.endereco.logradouro.bairro
        },
        numero: incorporadora.empresa.endereco.numero,
        complemento: incorporadora.empresa.endereco.complemento || '',
        ponto_referencia: incorporadora.empresa.endereco.ponto_referencia || '',
        latitude: incorporadora.empresa.endereco.latitude,
        longitude: incorporadora.empresa.endereco.longitude
      });
    }
  }, [incorporadora]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleEnderecoChange = (field: string, value: any) => {
    if (field.includes('logradouro.')) {
      const child = field.replace('logradouro.', '');
      setEnderecoData(prev => ({
        ...prev,
        logradouro: {
          ...prev.logradouro,
          [child]: value
        }
      }));
    } else if (field.includes('endereco.')) {
      const child = field.replace('endereco.', '');
      setEnderecoData(prev => ({
        ...prev,
        [child]: value
      }));
    }
  };

  const handleCepSearch = async (cep: string) => {
    if (cep.length === 8) {
      try {
        const data = await enderecoService.searchLogradouroByZipcode(cep);
        if (data.results && data.results.length > 0) {
          const endereco = data.results[0];
          setEnderecoData(prev => ({
            ...prev,
            logradouro: {
              ...prev.logradouro,
              nome: endereco.logradouro.nome,
              bairro: endereco.logradouro.bairro,
              cidade: endereco.logradouro.cidade?.id,
              zipcode: cep
            }
          }));
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error);
        alert('CEP não encontrado');
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
   const errors: string[] = [];

  const latitudeDigits = enderecoData.latitude.replace(/[\.\-]/g, '');
  const longitudeDigits = enderecoData.longitude.replace(/[\.\-]/g, '');

  if (latitudeDigits.length < 15 || latitudeDigits.length > 20) {
    errors.push('Latitude deve ter entre 15 e 20 dígitos');
  }

  if (longitudeDigits.length < 15 || longitudeDigits.length > 20) {
    errors.push('Longitude deve ter entre 15 e 20 dígitos');
  }

  if (errors.length > 0) {
    setError(errors.join(', ')); // Mostra o erro no mesmo lugar que você já tem
    setLoading(false);
    return; // ⚠️ impede o submit
  }

    try {
      console.log('Iniciando salvamento da incorporadora...');
      
      // Criar ou atualizar o endereço primeiro
      let enderecoId: number;
      
      // Converter para EnderecoCreate
      const enderecoCreateData: EnderecoCreate = {
        logradouro: {
          cidade: enderecoData.logradouro.cidade,
          zipcode: enderecoData.logradouro.zipcode,
          nome: enderecoData.logradouro.nome,
          bairro: enderecoData.logradouro.bairro
        },
        numero: enderecoData.numero,
        complemento: enderecoData.complemento,
        ponto_referencia: enderecoData.ponto_referencia,
        latitude: enderecoData.latitude,
        longitude: enderecoData.longitude
      };
      
      console.log('Dados do endereço:', enderecoCreateData);
      
      if (isEditing && incorporadora?.empresa?.endereco?.id) {
        // Se está editando, atualizar o endereço existente
        console.log('Atualizando endereço existente...');
        await enderecoService.updateEndereco(incorporadora.empresa.endereco.id, enderecoCreateData);
        enderecoId = incorporadora.empresa.endereco.id;
      } else {
        // Se é novo, criar o endereço
        console.log('Criando novo endereço...');
        const endereco = await enderecoService.createEndereco(enderecoCreateData);
        enderecoId = endereco.id;
        console.log('Endereço criado com ID:', enderecoId);
      }

      // Criar ou atualizar a empresa
      const empresaData: EmpresaInput = {
        ...formData,
        endereco: enderecoId
      };

      console.log('Dados da empresa:', empresaData);

      let empresaId: number;
      if (isEditing && incorporadora?.empresa?.id) {
        console.log('Atualizando empresa existente...');
        const empresa = await empresaService.atualizar(incorporadora.empresa.id, empresaData);
        empresaId = empresa.id;
      } else {
        console.log('Criando nova empresa...');
        const empresa = await empresaService.criar(empresaData);
        empresaId = empresa.id;
        console.log('Empresa criada com ID:', empresaId);
      }

      // Criar ou atualizar a incorporadora
      const incorporadoraData = { empresa: empresaId };
      console.log('Dados da incorporadora:', incorporadoraData);

      if (isEditing && incorporadoraId) {
        console.log('Atualizando incorporadora existente...');
        await incorporadoraService.atualizar(incorporadoraId, incorporadoraData);
      } else {
        console.log('Criando nova incorporadora...');
        await incorporadoraService.criar(incorporadoraData);
      }
      
      console.log('Incorporadora salva com sucesso!');
      navigate('/incorporadoras');
    } catch (err: any) {
      console.error('Erro detalhado ao salvar incorporadora:', err);
      console.error('Response data:', err.response?.data);
      console.error('Response status:', err.response?.status);
      console.error('Response headers:', err.response?.headers);
      setError(err.response?.data?.message || err.response?.data?.detail || err.message || 'Erro ao salvar incorporadora');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-6">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Informações da Empresa */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Informações da Empresa</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Razão Social *
              </label>
              <input
                type="text"
                name="razao_social"
                value={formData.razao_social}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Razão Social da empresa"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome Fantasia *
              </label>
              <input
                type="text"
                name="nome_fantasia"
                value={formData.nome_fantasia}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nome Fantasia da empresa"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                CNPJ *
              </label>
              <input
                type="text"
                name="cnpj"
                value={formData.cnpj}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="00.000.000/0000-00"
                required
              />
            </div>
          </div>
        </div>

        {/* Endereço */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Endereço</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                CEP *
              </label>
              <input
                type="text"
                value={enderecoData.logradouro.zipcode}
                onChange={(e) => {
                  const cep = e.target.value.replace(/\D/g, '');
                  handleEnderecoChange('logradouro.zipcode', cep);
                  if (cep.length === 8) {
                    handleCepSearch(cep);
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="00000000"
                maxLength={8}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Logradouro *
              </label>
              <input
                type="text"
                value={enderecoData.logradouro.nome}
                onChange={(e) => handleEnderecoChange('logradouro.nome', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nome da rua/avenida"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Número
              </label>
              <input
                type="number"
                value={enderecoData.numero || ''}
                onChange={(e) => handleEnderecoChange('endereco.numero', parseInt(e.target.value) || undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="123"
                min="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bairro *
              </label>
              <input
                type="text"
                value={enderecoData.logradouro.bairro}
                onChange={(e) => handleEnderecoChange('logradouro.bairro', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nome do bairro"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cidade *
              </label>
              <select
                value={enderecoData.logradouro.cidade}
                onChange={(e) => handleEnderecoChange('logradouro.cidade', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                disabled={loadingCidades}
              >
                <option value="">Selecione uma cidade</option>
                {cidades && cidades.map && cidades.map((cidade) => (
                  <option key={cidade.id} value={cidade.id}>
                    {cidade.nome} - {cidade.uf}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Complemento
              </label>
              <input
                type="text"
                value={enderecoData.complemento}
                onChange={(e) => handleEnderecoChange('endereco.complemento', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Apartamento, sala, etc."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ponto de Referência
              </label>
              <input
                type="text"
                value={enderecoData.ponto_referencia}
                onChange={(e) => handleEnderecoChange('endereco.ponto_referencia', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Próximo ao shopping, etc."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Latitude
              </label>
              <input
                type="text"
                value={enderecoData.latitude}
                onChange={(e) => handleEnderecoChange('endereco.latitude', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="-23.5505"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitude
              </label>
              <input
                type="text"
                value={enderecoData.longitude}
                onChange={(e) => handleEnderecoChange('endereco.longitude', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="-46.6333"
              />
            </div>
          </div>
        </div>

        {/* Botões */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/incorporadoras')}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Salvando...' : (isEditing ? 'Atualizar' : 'Criar')}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}
      </form>
    </div>
  );
}; 