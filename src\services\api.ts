import axios, { AxiosRequestHeaders } from 'axios';
import { tokenService } from './tokenService';
import { TokenResponse } from '../types/auth';

const api = axios.create({
  baseURL: '/api'  
});

const token = tokenService.getToken();
if (token) {
  api.defaults.headers.common['Authorization'] = `Bearer ${token.access}`;
}

api.interceptors.request.use((config) => {
  const token = tokenService.getToken();
  if (token) {
    if (!config.headers) {
      config.headers = {} as AxiosRequestHeaders;
    }
    config.headers['Authorization'] = `Bearer ${token.access}`;
  }
  return config;
}, (error) => {
  return Promise.reject(error);
});

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      tokenService.removeToken();
      delete api.defaults.headers.common['Authorization'];
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export default api; 