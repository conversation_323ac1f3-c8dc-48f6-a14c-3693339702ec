import { CrudService } from './crud';
import { Cidade, CidadeInput, CidadeUpdateInput, PaginatedCidadeResponse } from '../types/cidade';
import api from './api';

class CidadeService extends CrudService<Cidade> {
  constructor() {
    super('/cidades/');
  }

  async criar(cidade: CidadeInput): Promise<Cidade> {
    const response = await api.post<Cidade>('/cidades/', cidade);
    return response.data;
  }

  async buscarPorId(id: number): Promise<Cidade> {
    return this.get(id);
  }

  async atualizar(id: number, cidade: CidadeUpdateInput): Promise<Cidade> {
    return this.update(id, cidade);
  }

  async atualizarParcial(id: number, cidade: CidadeUpdateInput): Promise<Cidade> {
    return this.update(id, cidade);
  }

  async excluir(id: number): Promise<void> {
    return this.delete(id);
  }

  async listar(page: number = 1, limit: number = 10): Promise<PaginatedCidadeResponse> {
    const offset = (page - 1) * limit;
    const response = await api.get<PaginatedCidadeResponse>(`/cidades/?offset=${offset}&limit=${limit}`);
    return response.data;
  }
}

export const cidadeService = new CidadeService(); 