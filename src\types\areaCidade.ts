export interface Franqueado {
  id: number;
  nome: string;
  email: string;
  usuarios: User[];
  created: string;
  modified: string;
}

export interface User {
  id: number;
  email: string;
  nome_completo: string;
  cpf: string;
  data_nascimento?: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  created: string;
  modified: string;
  last_login?: string;
}

export interface AreaCidade {
  id: number;
  nome: string;
  cidade: number;
  franqueado: Franqueado;
  franqueado_id: number;
  created: string;
  modified: string;
}

export interface AreaCidadeInput {
  nome: string;
  cidade: number;
  franqueado_id: number;
}

export interface AreaCidadeUpdateInput extends Partial<AreaCidadeInput> {}

export interface PaginatedAreaCidadeResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: AreaCidade[];
} 