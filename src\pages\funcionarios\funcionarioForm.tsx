import React from 'react';
import { useForm, SubmitHandler, useWatch } from 'react-hook-form';

// Interface para as props do formulário
interface FuncionarioFormProps {
    onSubmit: SubmitHandler<FormData>;
    initialData?: Partial<FormData>;
    loading?: boolean;
    isEditing?: boolean;
    availableCargos?: string[];
}

// Interface para os dados do formulário, correspondendo aos campos
export interface FormData {
    nome: string;
    email: string;
    cpf: string;
    senha: string;
    cargos: string[];
}

const FuncionarioForm: React.FC<FuncionarioFormProps> = ({ onSubmit, initialData, loading, isEditing, availableCargos = [] }) => {
    const { register, handleSubmit, formState: { errors }, control } = useForm<FormData>({
        defaultValues: initialData
    });

    const watchedCargos = useWatch({ control, name: 'cargos', defaultValue: [] });
    const isGestorSelected = Array.isArray(watchedCargos) && watchedCargos.includes('GESTOR');

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
                <label htmlFor="nome" className="block text-sm font-medium text-gray-700">Nome Completo</label>
                <input
                    id="nome"
                    type="text"
                    {...register("nome", { required: "Nome é obrigatório" })}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
                {errors.nome && <p className="mt-1 text-sm text-red-600">{errors.nome.message}</p>}
            </div>

            <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
                <input
                    id="email"
                    type="email"
                    {...register("email", { required: "Email é obrigatório" })}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
                {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
            </div>

            <div>
                <label htmlFor="cpf" className="block text-sm font-medium text-gray-700">CPF</label>
                <input
                    id="cpf"
                    type="text"
                    {...register("cpf", { required: "CPF é obrigatório" })}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
                {errors.cpf && <p className="mt-1 text-sm text-red-600">{errors.cpf.message}</p>}
            </div>

            {!isEditing && (
                <div>
                    <label htmlFor="senha"  className="block text-sm font-medium text-gray-700">Senha</label>
                    <input
                        id="senha"
                        type="password"
                        {...register("senha", { required: "Senha é obrigatória" })}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    {errors.senha && <p className="mt-1 text-sm text-red-600">{errors.senha.message}</p>}
                </div>
            )}

            <div>
                <label className="block text-sm font-medium text-gray-700">Cargos</label>
                <div className="mt-2 space-y-2">
                    {availableCargos.map(cargo => {
                        const isOtherCargoAndGestorSelected = isGestorSelected && cargo !== 'GESTOR';
                        return (
                            <div key={cargo} className="flex items-center">
                                <input
                                    id={`cargo-${cargo}`}
                                    type="checkbox"
                                    value={cargo}
                                    {...register("cargos", { required: "Selecione ao menos um cargo" })}
                                    disabled={isOtherCargoAndGestorSelected}
                                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                                />
                                <label htmlFor={`cargo-${cargo}`} className="ml-2 block text-sm text-gray-900">
                                    {cargo}
                                </label>
                            </div>
                        );
                    })}
                </div>
                {errors.cargos && <p className="mt-1 text-sm text-red-600">{errors.cargos.message}</p>}
            </div>

            <button
                type="submit"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                disabled={loading}
            >
                {isEditing ? (loading ? 'Atualizando...' : 'Atualizar') : (loading ? 'Cadastrando...' : 'Cadastrar Membro')}
            </button>
        </form>
    );
};

export default FuncionarioForm;
