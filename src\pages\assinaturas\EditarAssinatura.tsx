import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AssinaturaForm } from './AssinaturaForm';
import { assinaturaService } from '../../services/assinaturaService';
import { Assinatura, AssinaturaUpdate } from '../../types/assinatura';

export const EditarAssinatura: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [assinatura, setAssinatura] = useState<Assinatura | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssinatura = async () => {
      if (!id) {
        setError('ID da assinatura não fornecido');
        setLoading(false);
        return;
      }

      try {
        const assinaturaData = await assinaturaService.buscarPorId(parseInt(id));
        setAssinatura(assinaturaData);
      } catch (err) {
        console.error('Erro ao carregar assinatura:', err);
        setError('Erro ao carregar dados da assinatura');
      } finally {
        setLoading(false);
      }
    };

    fetchAssinatura();
  }, [id]);

  const handleSubmit = async (data: AssinaturaUpdate) => {
    if (!id) return;
    
    setSaving(true);
    try {
      await assinaturaService.atualizar(parseInt(id), data);
      navigate('/assinaturas');
    } catch (error) {
      console.error('Erro ao atualizar assinatura:', error);
      alert('Erro ao atualizar assinatura. Tente novamente.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#32bef0]"></div>
      </div>
    );
  }

  if (error || !assinatura) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  {error || 'Assinatura não encontrada'}
                </h3>
                <button
                  onClick={() => navigate('/assinaturas')}
                  className="mt-2 text-sm text-red-600 hover:text-red-500"
                >
                  Voltar para lista
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-[#29306a]">
            Editar Assinatura
          </h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Atualize os dados da assinatura
          </p>
        </div>

        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <AssinaturaForm
              initialData={{
                recorrencia: assinatura.recorrencia,
                data_expiracao: assinatura.data_expiracao,
                forma_pagamento: assinatura.forma_pagamento,
                assinante: assinatura.assinante,
                cidade: assinatura.cidade,
                plano: assinatura.plano
              }}
              onSubmit={handleSubmit}
              loading={saving}
              isEditing={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
}; 