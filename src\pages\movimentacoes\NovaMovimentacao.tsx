import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { movimentacaoService } from '../../services/movimentacaoService';
import { MovimentacaoCreate } from '../../types/movimentacao';
import {
  TIPO_MOVIMENTACAO_OPTIONS,
  MES_MOVIMENTACAO_OPTIONS,
  ORIGEM_RECURSO_OPTIONS,
  MOTIVO_DISTRATO_OPTIONS
} from '../../types/movimentacao';

const NovaMovimentacao: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<MovimentacaoCreate>({
    tipo_movimentacao: 1,
    quantidade: undefined,
    ano_movimentacao: undefined,
    mes_movimentacao: undefined,
    origem_recurso: 1,
    motivo_distrato: undefined,
    was_lancamento: false
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else if (type === 'number') {
      const numValue = value === '' ? undefined : parseInt(value);
      setFormData(prev => ({
        ...prev,
        [name]: numValue
      }));
    } else {
      const numValue = parseInt(value);
      setFormData(prev => ({
        ...prev,
        [name]: isNaN(numValue) ? undefined : numValue
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await movimentacaoService.create(formData);
      navigate('/movimentacoes');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erro ao criar movimentação');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full">
      <div className="max-w-2xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Nova Movimentação</h1>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong className="font-bold">Erro:</strong>
            <span className="block sm:inline"> {error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="tipo_movimentacao">
              Tipo de Movimentação *
            </label>
            <select
              id="tipo_movimentacao"
              name="tipo_movimentacao"
              value={formData.tipo_movimentacao}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            >
              {TIPO_MOVIMENTACAO_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="quantidade">
              Quantidade
            </label>
            <input
              type="number"
              id="quantidade"
              name="quantidade"
              value={formData.quantidade || ''}
              onChange={handleInputChange}
              min="1"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="ano_movimentacao">
              Ano da Movimentação
            </label>
            <input
              type="number"
              id="ano_movimentacao"
              name="ano_movimentacao"
              value={formData.ano_movimentacao || ''}
              onChange={handleInputChange}
              min="2000"
              max="2025"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="mes_movimentacao">
              Mês da Movimentação
            </label>
            <select
              id="mes_movimentacao"
              name="mes_movimentacao"
              value={formData.mes_movimentacao || ''}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            >
              <option value="">Selecione um mês</option>
              {MES_MOVIMENTACAO_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="origem_recurso">
              Origem do Recurso *
            </label>
            <select
              id="origem_recurso"
              name="origem_recurso"
              value={formData.origem_recurso}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            >
              {ORIGEM_RECURSO_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {formData.tipo_movimentacao === 3 && (
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="motivo_distrato">
                Motivo do Distrato
              </label>
              <select
                id="motivo_distrato"
                name="motivo_distrato"
                value={formData.motivo_distrato || ''}
                onChange={handleInputChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              >
                <option value="">Selecione um motivo</option>
                {MOTIVO_DISTRATO_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="mb-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="was_lancamento"
                checked={formData.was_lancamento}
                onChange={handleInputChange}
                className="mr-2"
              />
              <span className="text-gray-700 text-sm font-bold">
                É um lançamento
              </span>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <button
              type="button"
              onClick={() => navigate('/movimentacoes')}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              disabled={loading}
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50"
              disabled={loading}
            >
              {loading ? 'Salvando...' : 'Salvar'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NovaMovimentacao; 