import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { franquiaService } from "../../services/franquiaService";
import { PaginatedTable, Column } from "../../components/Table/PaginatedTable";
import { MembroFranquia } from "../../types/franquia";
import { useAuth } from "../../contexts/AuthContext";

export const ListaFuncionarios: React.FC = () => {
  const { user } = useAuth();
  const [allFuncionarios, setAllFuncionarios] = useState<MembroFranquia[]>([]);
  const [displayedFuncionarios, setDisplayedFuncionarios] = useState<
    MembroFranquia[]
  >([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [filters, setFilters] = useState({
    search: "",
    cargo: "",
  });

  const fetchFuncionarios = async () => {
    if (!user || !user.franquia_membership) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await franquiaService.listarMembros(
        user.franquia_membership.franquia_id,
        pageSize,
        (currentPage - 1) * pageSize,
        filters.search
      );
      setAllFuncionarios(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error("Erro ao buscar funcionários:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFuncionarios();
  }, [currentPage, filters.search, user]);

  // Aplica o filtro de cargo no lado do cliente
  useEffect(() => {
    let filteredData = allFuncionarios;

    if (filters.cargo) {
      filteredData = allFuncionarios.filter((f) =>
        f.cargos.includes(filters.cargo)
      );
    }

    setDisplayedFuncionarios(filteredData);
    // Nota: A contagem total para paginação não é atualizada por este filtro,
    // pois a filtragem de cargo é feita no cliente e a paginação é no servidor.
  }, [allFuncionarios, filters.cargo]);

  const columns: Column<MembroFranquia>[] = [
    {
      key: "user" as keyof MembroFranquia,
      title: "Nome",
      render: (value: any, record: MembroFranquia) => (
        <span className="font-medium text-gray-900">
          {record.user.nome_completo}
        </span>
      ),
    },
    {
      key: "user" as keyof MembroFranquia,
      title: "Email",
      render: (value: any, record: MembroFranquia) => (
        <span>{record.user.email || "Não informado"}</span>
      ),
    },
    {
      key: "cargos",
      title: "Cargos",
      render: (value: any, record: MembroFranquia) => (
        <span>{record.cargos.join(", ") || "Não informado"}</span>
      ),
    },
    {
      key: "status_na_franquia",
      title: "Status",
      render: (value: any, record: MembroFranquia) => (
        <span>{record.status_na_franquia || "Não informado"}</span>
      ),
    },
    {
      key: "user" as keyof MembroFranquia,
      title: "Ações",
      render: (value: any, record: MembroFranquia) => (
        <div className="flex space-x-2">
          <Link
            to={`/funcionarios/${record.user.id}/editar`}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
          >
            Editar
          </Link>
        </div>
      ),
    },
  ];

  const handleFilterChange = (field: string, value: string) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
    if (field !== "cargo") {
      setCurrentPage(1);
    }
  };

  const limparFiltros = () => {
    setFilters({ search: "", cargo: "" });
    setCurrentPage(1);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          Funcionários da Franquia
        </h1>
        <Link
          to="/funcionarios/novo"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Novo Funcionário
        </Link>
      </div>

      {/* Filtros */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-lg font-semibold mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome do Funcionário
            </label>
            <input
              type="text"
              placeholder="Digite o nome..."
              value={filters.search}
              onChange={(e) => handleFilterChange("search", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cargo
            </label>
            <select
              value={filters.cargo}
              onChange={(e) => handleFilterChange("cargo", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todos</option>
              <option value="ADMIN">Admin</option>
              <option value="VENDEDOR">Vendedor</option>
              <option value="GESTOR">Gestor</option>
              <option value="FINANCEIRO">Financeiro</option>
              <option value="CADASTRAMENTO">Cadastramento</option>
            </select>
          </div>
        </div>

        <div className="mt-4">
          <button
            onClick={limparFiltros}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Limpar Filtros
          </button>
        </div>
      </div>

      {/* Tabela */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <PaginatedTable
          data={displayedFuncionarios}
          columns={columns}
          loading={loading}
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / pageSize)}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  );
};
