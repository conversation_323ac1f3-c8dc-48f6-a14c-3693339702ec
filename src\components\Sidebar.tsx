import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface SidebarProps {
  onLogout: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ onLogout }) => {
  const [isOpen, setIsOpen] = useState(true);
  const [menuItems, setMenuItems] = useState<{ path: string; label: string }[]>([]);
  const { user } = useAuth();

  useEffect(() => {
    const baseMenu = [
      { path: '/Listavincular', label: 'Solicitação' },
      { path: '/funcionarios', label: 'Funcionários' },
      { path: '/assinantes', label: 'Assinantes' },
      { path: '/planos', label: 'Planos' },
      { path: '/empreendimentos', label: 'Empreendimentos' },
      { path: '/incorporadoras', label: 'Incorporadoras' },
      { path: '/movimentacoes', label: 'Movimentações' },
    ];

    if (user?.tipo === 'TACT') {
      baseMenu.push({ path: '/areas-cidade', label: 'Áreas-Cidade' });
      baseMenu.push({ path: '/cidades', label: 'Cidades' });
    }

    setMenuItems(baseMenu);
  }, [user]);

  return (
    <>
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
      <div className={`bg-white shadow-lg w-64 fixed h-full transition-transform duration-300 ease-in-out z-30 ${
        isOpen ? 'translate-x-0' : '-translate-x-64'
      }`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <img
              src= "/TACTGEO_Logo.png" 
              alt="Logo Tact IMOBI"
              className="h-12 w-auto max-w-[250px]" 
              loading="lazy"
              draggable={false}
            />
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="text-gray-500 hover:text-gray-700 lg:hidden"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      <nav className="mt-4">
        <div className="px-4 space-y-1">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className="block px-4 py-2 text-[#64748b] hover:bg-[#f8fafc] hover:text-[#29306a] rounded-lg transition-colors duration-200"
            >
              {item.label}
            </Link>
          ))}
        </div>
        
        <div className="px-4 mt-8 pt-4 border-t border-gray-200">
          <Link
            to="/configuracoes"
            className="flex items-center px-4 py-2 text-[#64748b] hover:bg-[#f8fafc] hover:text-[#29306a] rounded-lg transition-colors duration-200"
          >
            <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Configurações
          </Link>
        </div>
        
        <div className="px-4 mt-4">
          <button
            onClick={onLogout}
            className="w-full px-4 py-2 text-sm font-medium rounded-md text-white bg-[#f55eeb] hover:bg-[#e54dd8] transition-colors duration-200"
          >
            Sair
          </button>
        </div>
      </nav>

      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`fixed z-20 left-4 top-4 lg:hidden p-2 rounded-md bg-white shadow-lg text-gray-500 hover:text-gray-700 ${
          isOpen ? 'hidden' : 'block'
        }`}
      >
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>
    </>
  );
};