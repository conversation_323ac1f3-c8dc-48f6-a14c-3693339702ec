export interface User {
  id: number;
  email: string;
  nome_completo: string;
  cpf: string;
  data_nascimento?: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  created: string;
  modified: string;
  last_login?: string;
}

export interface Franqueado {
  id: number;
  usuarios: User[];
  nome: string;
  email: string;
  created: string;
  modified: string;
}

export interface FranqueadoInput {
  nome: string;
  email: string;
}

export interface FranqueadoUpdateInput extends Partial<FranqueadoInput> {}

export interface PaginatedFranqueadoResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Franqueado[];
} 