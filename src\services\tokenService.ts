import { TokenResponse } from '../types/auth';

const TOKEN_KEY = '@TactImobi:token';

export const tokenService = {
  saveToken(token: TokenResponse): void {
    localStorage.setItem(TOKEN_KEY, JSON.stringify(token));
  },

  getToken(): TokenResponse | null {
    const token = localStorage.getItem(TOKEN_KEY);
    if (token) {
      try {
        return JSON.parse(token) as TokenResponse;
      } catch {
        return null;
      }
    }
    return null;
  },

  removeToken(): void {
    localStorage.removeItem(TOKEN_KEY);
  },

  hasToken(): boolean {
    return !!this.getToken();
  }
}; 