import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { PlanoForm } from './PlanoForm';
import { planoService } from '../../services/planoService';
import { PlanoInput } from '../../types/plano';

export const EditarPlano: React.FC = () => {
  const { id } = useParams();
  const [plano, setPlano] = useState<PlanoInput | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlano = async () => {
      if (!id) return;

      try {
        const data = await planoService.buscarPorId(Number(id));
        setPlano({
          nome: data.nome,
          descricao: data.descricao,
          valor: data.valor
        });
      } catch (err) {
        console.error('Erro ao carregar plano:', err);
        setError('Erro ao carregar dados do plano');
      } finally {
        setLoading(false);
      }
    };

    fetchPlano();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#32bef0]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">{error}</h3>
          </div>
        </div>
      </div>
    );
  }

  if (!plano) {
    return (
      <div className="rounded-md bg-yellow-50 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Plano não encontrado</h3>
          </div>
        </div>
      </div>
    );
  }

  return <PlanoForm initialData={plano} isEditing={true} />;
}; 