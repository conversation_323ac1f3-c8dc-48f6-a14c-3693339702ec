import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { Login } from './pages/Login';
import { Register } from './pages/Register';
import { ListaCidades } from './pages/cidades/ListaCidades';
import { NovaCidade } from './pages/cidades/NovaCidade';
import { EditarCidade } from './pages/cidades/EditarCidade';
import { ListaAreasCidade } from './pages/areas-cidade/ListaAreasCidade';
import { NovaAreaCidade } from './pages/areas-cidade/NovaAreaCidade';
import { EditarAreaCidade } from './pages/areas-cidade/EditarAreaCidade';
import { ListaPlanos } from './pages/planos/ListaPlanos';
import { NovoPlano } from './pages/planos/NovoPlano';
import { EditarPlano } from './pages/planos/EditarPlano';
import { ListaIncorporadoras } from './pages/incorporadoras/ListaIncorporadoras';
import { NovaIncorporadora } from './pages/incorporadoras/NovaIncorporadora';
import { EditarIncorporadora } from './pages/incorporadoras/EditarIncorporadora';
import { ListaFranqueados } from './pages/franqueados/ListaFranqueados';
import { NovoFranqueado } from'./pages/franqueados/NovoFranqueado';
import { EditarFranqueado } from './pages/franqueados/EditarFranqueado';
import { ListaFuncionarios } from './pages/funcionarios/ListaFuncionarios';
import { Novofuncionario } from './pages/funcionarios/Novofuncionario';
import { EditarAssinante } from './pages/funcionarios/EditarAssinante';
import { Financeiro } from './pages/funcionarios/Financeiro';
import { ListaAssinantes as ListaAssinantesPage } from './pages/assinantes/ListaAssinantes'; // Importação correta para Assinantes
import { NovoAssinante } from './pages/assinantes/NovoAssinante'; // Importação para NovoAssinante
import { EditarAssinante as EditarAssinantePage } from './pages/assinantes/EditarAssinante'; // Importação para EditarAssinante
import { Listavincular } from './pages/assinaturas/Listavincular';
import { NovaAssinatura } from './pages/assinaturas/NovaAssinatura';
import { EditarAssinatura } from './pages/assinaturas/EditarAssinatura';
import ListaMovimentacoes from './pages/movimentacoes/ListaMovimentacoes';
import NovaMovimentacao from './pages/movimentacoes/NovaMovimentacao';
import { ConfiguracoesPerfil } from './pages/ConfiguracoesPerfil';
import ListaEmpreendimentos from './pages/empreendimentos/ListaEmpreendimentos';
import NovoEmpreendimento from './pages/empreendimentos/NovoEmpreendimento';
import EditarEmpreendimento from './pages/empreendimentos/EditarEmpreendimento';
import VisualizarEmpreendimento from './pages/empreendimentos/VisualizarEmpreendimento';
import TesteEmpreendimento from './pages/empreendimentos/TesteEmpreendimento';
import { Sidebar } from './components/Sidebar';
import './styles/colors.css';

function AppContent() {
  const { isAuthenticated, loading, login, logout } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#29306a] to-[#32bef0]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <Router>
      <div className="min-h-screen bg-[#f8fafc]">
        {isAuthenticated ? (
          <div className="flex">
            <Sidebar onLogout={logout} />
            <main className="flex-1 p-4 lg:p-8 ml-64 lg:ml-64 md:ml-0 sm:ml-0 min-h-screen w-full">
              <Routes>
                <Route path="/" element={<Navigate to="/cidades" replace />} />
                <Route path="/cidades" element={<ListaCidades />} />
                <Route path="/cidades/novo" element={<NovaCidade />} />
                <Route path="/cidades/:id/editar" element={<EditarCidade />} />
                <Route path="/areas-cidade" element={<ListaAreasCidade />} />
                <Route path="/areas-cidade/novo" element={<NovaAreaCidade />} />
                <Route path="/areas-cidade/:id/editar" element={<EditarAreaCidade />} />
                <Route path="/planos" element={<ListaPlanos />} />
                <Route path="/planos/novo" element={<NovoPlano />} />
                <Route path="/planos/:id/editar" element={<EditarPlano />} />
                <Route path="/incorporadoras" element={<ListaIncorporadoras />} />
                <Route path="/incorporadoras/novo" element={<NovaIncorporadora />} />
                <Route path="/incorporadoras/:id/editar" element={<EditarIncorporadora />} />
                <Route path="/franqueados" element={<ListaFranqueados />} />
                <Route path="/franqueados/novo" element={<NovoFranqueado />} />
                <Route path="/franqueados/:id/editar" element={<EditarFranqueado />} />
                <Route path="/funcionarios" element={<ListaFuncionarios />} />
                <Route path="/funcionarios/novo" element={<Novofuncionario />} />
                <Route path="/funcionarios/:id/editar" element={<EditarAssinante />} />
                <Route path="/funcionarios/:id/financeiro" element={<Financeiro />} />
                <Route path="/assinantes" element={<ListaAssinantesPage />} />
                <Route path="/assinantes/novo" element={<NovoAssinante />} />
                <Route path="/assinantes/:id/editar" element={<EditarAssinantePage />} />
                <Route path="/assinantes/:id/financeiro" element={<Financeiro />} />
                <Route path="/Listavincular" element={<Listavincular />} />
                <Route path="/assinaturas/novo" element={<NovaAssinatura />} />
                <Route path="/assinaturas/:id/editar" element={<EditarAssinatura />} />
                <Route path="/empreendimentos" element={<ListaEmpreendimentos />} />
                <Route path="/empreendimentos/novo" element={<NovoEmpreendimento />} />
                <Route path="/empreendimentos/editar/:id" element={<EditarEmpreendimento />} />
                <Route path="/empreendimentos/:id/visualizar" element={<VisualizarEmpreendimento />} />
                <Route path="/empreendimentos/teste" element={<TesteEmpreendimento />} />
                <Route path="/movimentacoes" element={<ListaMovimentacoes />} />
                <Route path="/movimentacoes/nova" element={<NovaMovimentacao />} />
                <Route path="/configuracoes" element={<ConfiguracoesPerfil />} />
                <Route path="*" element={<Navigate to="/cidades" replace />} />
              </Routes>
            </main>
          </div>
        ) : (
          <Routes>
            <Route path="/login" element={<Login onLoginSuccess={login} />} />
            <Route path="/register" element={<Register />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        )}
      </div>
    </Router>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
