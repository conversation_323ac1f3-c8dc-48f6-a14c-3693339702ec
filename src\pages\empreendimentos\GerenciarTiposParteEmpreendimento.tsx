import React, { useState, useEffect } from 'react';
import { TipoParteEmpreendimentoService } from '../../services/empreendimentoService';
import { TipoParteEmpreendimento, TipoParteEmpreendimentoCreate } from '../../types/empreendimento';

interface GerenciarTiposParteEmpreendimentoProps {
  onClose: () => void;
  onTipoSelected?: (tipo: TipoParteEmpreendimento) => void;
}

const GerenciarTiposParteEmpreendimento: React.FC<GerenciarTiposParteEmpreendimentoProps> = ({
  onClose,
  onTipoSelected
}) => {
  const [tipos, setTipos] = useState<TipoParteEmpreendimento[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingTipo, setEditingTipo] = useState<TipoParteEmpreendimento | null>(null);
  const [formData, setFormData] = useState<TipoParteEmpreendimentoCreate>({
    Nome_tipo_parte_empreendimento: '',
    descricao: '',
    atributos_definidos: {}
  });
  const [editingAtributoKey, setEditingAtributoKey] = useState<string | null>(null);
  const [atributoDraft, setAtributoDraft] = useState<any>({
    key: '',
    label: '',
    type: 'text',
    required: false,
    placeholder: '',
    options: '',
    min: '',
    max: '',
    step: ''
  });

  const tipoParteEmpreendimentoService = new TipoParteEmpreendimentoService();

  useEffect(() => {
    loadTipos();
  }, []);

  const loadTipos = async () => {
    try {
      const tiposData = await tipoParteEmpreendimentoService.getTipos();
      setTipos(tiposData);
    } catch (error) {
      console.error("Erro ao carregar tipos de parte de empreendimento:", error);
    }
};

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.Nome_tipo_parte_empreendimento.trim()) {
      alert('Nome é obrigatório');
      return;
    }

    if (editingTipo) {
      tipoParteEmpreendimentoService.updateTipo(Number(editingTipo.id), formData);
    } else {
      tipoParteEmpreendimentoService.addTipo(formData);
    }

    setFormData({ Nome_tipo_parte_empreendimento: '', descricao: '', atributos_definidos: {} });
    setEditingTipo(null);
    setShowForm(false);
    loadTipos();
  };

  const handleEdit = (tipo: TipoParteEmpreendimento) => {
    setEditingTipo(tipo);
    setFormData({
      Nome_tipo_parte_empreendimento: tipo.Nome_tipo_parte_empreendimento,
      descricao: tipo.descricao || '',
      atributos_definidos: tipo.atributos_definidos
    });
    setShowForm(true);
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Tem certeza que deseja excluir este tipo?')) {
      tipoParteEmpreendimentoService.deleteTipo(Number(id));
      loadTipos();
    }
  };

  const handleSelect =  (tipo: TipoParteEmpreendimento) => {
    if (onTipoSelected) {
       onTipoSelected(tipo);
      onClose();
    }
  };

  const addAtributo = () => {
    setEditingAtributoKey(null);
    setAtributoDraft({
      key: '',
      label: '',
      type: 'text',
      required: false,
      placeholder: '',
      options: '',
      min: '',
      max: '',
      step: ''
    });
    setShowAtributoModal(true);
  };

  const editAtributo = (key: string, attr: any) => {
    setEditingAtributoKey(key);
    setAtributoDraft({
      key,
      label: attr.label || '',
      type: attr.type || 'text',
      required: attr.required || false,
      placeholder: attr.placeholder || '',
      options: (attr.options || []).join(', '),
      min: attr.min || '',
      max: attr.max || '',
      step: attr.step || ''
    });
    setShowAtributoModal(true);
  };

  const removeAtributo = (key: string) => {
    const newAtributos = { ...formData.atributos_definidos };
    delete newAtributos[key];
    setFormData(prev => ({
      ...prev,
      atributos_definidos: newAtributos
    }));
  };

  const [showAtributoModal, setShowAtributoModal] = useState(false);

  const handleAtributoSave = () => {
    if (!atributoDraft.key.trim() || !atributoDraft.label.trim()) {
      alert('Identificador e Nome do Campo são obrigatórios');
      return;
    }
    const key = atributoDraft.key.trim();
    const attr = {
      label: atributoDraft.label,
      type: atributoDraft.type,
      required: atributoDraft.required,
      placeholder: atributoDraft.placeholder,
      options: (atributoDraft.type === 'select' || atributoDraft.type === 'multiselect') ? atributoDraft.options.split(',').map((s: string) => s.trim()).filter(Boolean) : undefined,
      min: atributoDraft.type === 'number' ? atributoDraft.min : undefined,
      max: atributoDraft.type === 'number' ? atributoDraft.max : undefined,
      step: atributoDraft.type === 'number' ? atributoDraft.step : undefined
    };
    setFormData(prev => ({
      ...prev,
      atributos_definidos: {
        ...prev.atributos_definidos,
        [key]: attr
      }
    }));
    setShowAtributoModal(false);
    setEditingAtributoKey(null);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Gerenciar Tipologias
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {!showForm ? (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Tipos Cadastrados</h3>
              <button
                onClick={() => setShowForm(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Novo Tipo
              </button>
            </div>

            <div className="grid gap-4">
              {tipos.map((tipo) => (
                <div key={tipo.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{tipo.Nome_tipo_parte_empreendimento}</h4>
                      {tipo.descricao && (
                        <p className="text-sm text-gray-600 mt-1">{tipo.descricao}</p>
                      )}
                      {Object.keys(tipo.atributos_definidos).length > 0 && (
                        <div className="mt-2">
                          <p className="text-sm font-medium text-gray-700">Atributos:</p>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {Object.entries(tipo.atributos_definidos).map(([key, attr]) => (
                              <span key={key} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                {key} ({attr.type})
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex space-x-2 ml-4">
                      {onTipoSelected && (
                        <button
                          onClick={() => handleSelect(tipo)}
                          className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                        >
                          Selecionar
                        </button>
                      )}
                      <button
                        onClick={() => handleEdit(tipo)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                      >
                        Editar
                      </button>
                      <button
                        onClick={() => handleDelete(tipo.id)}
                        className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                      >
                        Excluir
                      </button>
                    </div>
                  </div>
                </div>
              ))}
              
              {tipos.length === 0 && (
                <p className="text-gray-500 text-center py-8">
                  Nenhum tipo cadastrado. Clique em "Novo Tipo" para começar.
                </p>
              )}
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">
              {editingTipo ? 'Editar Tipo' : 'Novo Tipo'}
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome *
              </label>
              <input
                type="text"
                value={formData.Nome_tipo_parte_empreendimento}
                onChange={(e) => setFormData(prev => ({ ...prev, Nome_tipo_parte_empreendimento: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: Apartamento 2 quartos"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={formData.descricao}
                onChange={(e) => setFormData(prev => ({ ...prev, descricao: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Descrição do tipo"
                rows={3}
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Atributos Definidos
                </label>
                <button
                  type="button"
                  onClick={addAtributo}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                >
                  + Adicionar Atributo
                </button>
              </div>
              
              <div className="border rounded-lg p-4 min-h-[100px]">
                {Object.keys(formData.atributos_definidos).length === 0 ? (
                  <p className="text-gray-500 text-center py-4">
                    Nenhum atributo definido. Clique em "Adicionar Atributo" para começar.
                  </p>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(formData.atributos_definidos).map(([key, attr]) => (
                      <div key={key} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span className="text-sm">
                          <strong>{key}</strong> ({attr.type}) - {attr.label}
                        </span>
                        <div className="flex space-x-2">
                          <button
                            type="button"
                            onClick={() => editAtributo(key, attr)}
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            Editar
                          </button>
                          <button
                            type="button"
                            onClick={() => removeAtributo(key)}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            Remover
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingTipo(null);
                  setFormData({ Nome_tipo_parte_empreendimento: '', descricao: '', atributos_definidos: {} });
                }}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {editingTipo ? 'Atualizar' : 'Criar'}
              </button>
            </div>
          </form>
        )}
      </div>
      {/* Modal de atributo */}
      {showAtributoModal && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h4 className="text-lg font-semibold mb-4">{editingAtributoKey ? 'Editar' : 'Novo'} Atributo</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Identificador *</label>
                <input
                  type="text"
                  value={atributoDraft.key}
                  onChange={e => setAtributoDraft((prev: any) => ({ ...prev, key: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  disabled={!!editingAtributoKey}
                  placeholder="Ex: quartos"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome do Campo *</label>
                <input
                  type="text"
                  value={atributoDraft.label}
                  onChange={e => setAtributoDraft((prev: any) => ({ ...prev, label: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Ex: Número de Quartos"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tipo *</label>
                <select
                  value={atributoDraft.type}
                  onChange={e => setAtributoDraft((prev: any) => ({ ...prev, type: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                >
                  <option value="text">Texto</option>
                  <option value="number">Número</option>
                  <option value="select">Seleção</option>
                  <option value="multiselect">Múltipla Seleção</option>
                  <option value="checkbox">Checkbox</option>
                  <option value="textarea">Área de Texto</option>
                </select>
              </div>
              {(atributoDraft.type === 'select' || atributoDraft.type === 'multiselect') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Opções (separadas por vírgula)</label>
                  <input
                    type="text"
                    value={atributoDraft.options}
                    onChange={e => setAtributoDraft((prev: any) => ({ ...prev, options: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Ex: Alto, Médio, Baixo"
                  />
                </div>
              )}
              {atributoDraft.type === 'number' && (
                <div className="grid grid-cols-3 gap-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Mínimo</label>
                    <input
                      type="number"
                      value={atributoDraft.min}
                      onChange={e => setAtributoDraft((prev: any) => ({ ...prev, min: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Máximo</label>
                    <input
                      type="number"
                      value={atributoDraft.max}
                      onChange={e => setAtributoDraft((prev: any) => ({ ...prev, max: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Passo</label>
                    <input
                      type="number"
                      value={atributoDraft.step}
                      onChange={e => setAtributoDraft((prev: any) => ({ ...prev, step: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              )}
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={atributoDraft.required}
                    onChange={e => setAtributoDraft((prev: any) => ({ ...prev, required: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-sm">Obrigatório</span>
                </label>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Placeholder</label>
                <input
                  type="text"
                  value={atributoDraft.placeholder}
                  onChange={e => setAtributoDraft((prev: any) => ({ ...prev, placeholder: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Ex: Digite o valor"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-4 mt-6">
              <button
                type="button"
                onClick={() => setShowAtributoModal(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                type="button"
                onClick={handleAtributoSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Salvar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GerenciarTiposParteEmpreendimento; 