export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface Assinatura {
  id: number;
  plano: number;
  franqueado: number;
  data_inicio: string;
  data_fim: string;
  status: 'ativo' | 'inativo' | 'cancelado';
  valor: number;
}

export interface Cidade {
  id: number;
  nome: string;
  estado: string;
  populacao: number;
  area: number;
  densidade_demografica: number;
  pib: number;
  idhm: number;
}

export interface Empreendimento {
  id: number;
  nome: string;
  incorporadora: number;
  cidade: number;
  endereco: string;
  descricao: string;
  status: 'lancamento' | 'em_construcao' | 'pronto';
  data_lancamento: string;
  data_entrega: string;
  valor_metro_quadrado: number;
}

export interface Franqueado {
  id: number;
  nome: string;
  email: string;
  telefone: string;
  cidade: number;
  cnpj: string;
  razao_social: string;
  status: 'ativo' | 'inativo';
}

export interface Incorporadora {
  id: number;
  nome: string;
  cnpj: string;
  razao_social: string;
  email: string;
  telefone: string;
  website: string;
  status: 'ativa' | 'inativa';
}

export interface Movimentacao {
  id: number;
  assinatura: number;
  tipo: 'pagamento' | 'reembolso' | 'cancelamento';
  valor: number;
  data: string;
  status: 'pendente' | 'confirmado' | 'cancelado';
  descricao: string;
}

export interface Plano {
  id: number;
  nome: string;
  descricao: string;
  valor_mensal: number;
  valor_anual: number;
  status: 'ativo' | 'inativo';
  recursos: string[];
} 