import api from './api';
import { tokenService } from './tokenService';
import { UserLogin, LoginCredentials, UserRegister, AuthResponse, User, TokenResponse, UserProfile, ChangePassword } from '../types/auth';
import axios from 'axios';

const setAuthHeader = (token: TokenResponse | null) => {
  if (token) {
    api.defaults.headers.common['Authorization'] = `Bearer ${token.access}`;
  } else {
    delete api.defaults.headers.common['Authorization'];
  }
};

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const loginData: UserLogin = {
        login: credentials.email,
        password: credentials.password
      };
      
      const response = await api.post<AuthResponse>('/auth/login/', loginData);
      
      if (response.data.token) {
        tokenService.saveToken(response.data.token);
        setAuthHeader(response.data.token);
      }
      return response.data;
    } catch (error) {
      tokenService.removeToken();
      setAuthHeader(null);
      throw error;
    }
  }

  async register(data: UserRegister): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>('/auth/register/', data);
      if (response.data.token) {
        tokenService.saveToken(response.data.token);
        setAuthHeader(response.data.token);
      }
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout/');
    } finally {
      tokenService.removeToken();
      setAuthHeader(null);
    }
  }

  async getProfile(): Promise<User> {
    try {
      const response = await api.get<User>('/auth/profile/');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        tokenService.removeToken();
        setAuthHeader(null);
      }
      throw error;
    }
  }

  async updateProfile(data: Partial<UserProfile>): Promise<User> {
    const response = await api.patch<User>('/auth/profile/', data);
    return response.data;
  }

  async changePassword(data: ChangePassword): Promise<void> {
    await api.post('/auth/change-password/', data);
  }

  async sendResetPasswordLink(email: string): Promise<void> {
    await api.post('/auth/send-reset-password-link/', { login: email });
  }

  isAuthenticated(): boolean {
    const token = tokenService.getToken();
    if (token) {
      setAuthHeader(token);
      return true;
    }
    return false;
  }

  getToken(): TokenResponse | null {
    return tokenService.getToken();
  }
}

export const authService = new AuthService(); 