export interface User {
  id: number;
  email: string;
  nome_completo: string;
  cpf: string;
  data_nascimento?: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  created: string;
  modified: string;
  last_login?: string;
}

export interface Funcionario {
  user: User;
  cargos: string[];
  status_na_franquia: string;
  excluido_em: string | null;
  created: string;
  modified: string;
}

export interface FuncionarioCreate {
  nome_completo: string;
  email: string;
  cpf: string;
  password: string;
  cargo: string;
}

export interface FuncionarioUpdate {
  nome?: string;
  email?: string;
  usuario_id?: number;
  cargo?: string[];
}

export interface PaginatedFuncionarioList {
  count: number;
  next?: string;
  previous?: string;
  results: Funcionario[];
} 