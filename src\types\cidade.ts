export interface Cidade {
  id: number;
  nome: string;
  estado: string;
  uf: string;
  pais: string;
  created: string;
  modified: string;
}

export interface CidadeInput {
  nome: string;
  estado: string;
  uf: string;
  pais: string;
}

export interface CidadeUpdateInput extends Partial<CidadeInput> {}

export interface PaginatedCidadeResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Cidade[];
} 