import { CrudService } from './crud';
import { AreaCidade, AreaCidadeInput, AreaCidadeUpdateInput, PaginatedAreaCidadeResponse } from '../types/areaCidade';
import api from './api';

class AreaCidadeService extends CrudService<AreaCidade> {
  constructor() {
    super('/areas-cidade/');
  }

  async criar(areaCidade: AreaCidadeInput): Promise<AreaCidade> {
    const response = await api.post<AreaCidade>('/areas-cidade/', areaCidade);
    return response.data;
  }

  async buscarPorId(id: number): Promise<AreaCidade> {
    return this.get(id);
  }

  async atualizar(id: number, areaCidade: AreaCidadeUpdateInput): Promise<AreaCidade> {
    return this.update(id, areaCidade);
  }

  async atualizarParcial(id: number, areaCidade: AreaCidadeUpdateInput): Promise<AreaCidade> {
    return this.update(id, areaCidade);
  }

  async excluir(id: number): Promise<void> {
    return this.delete(id);
  }

  async listar(page: number = 1, limit: number = 10): Promise<PaginatedAreaCidadeResponse> {
    const offset = (page - 1) * limit;
    const response = await api.get<PaginatedAreaCidadeResponse>(`/areas-cidade/?offset=${offset}&limit=${limit}`);
    return response.data;
  }
}

export const areaCidadeService = new AreaCidadeService(); 