import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { franqueadoService } from '../../services/franqueadoService';
import { Franqueado } from '../../types/franqueado';

export const ListaFranqueados: React.FC = () => {
  const [franqueados, setFranqueados] = useState<Franqueado[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFranqueados = async () => {
      try {
        const response = await franqueadoService.listar();
        setFranqueados(response.results);
      } catch (err) {
        console.error('Erro ao carregar franqueados:', err);
        setError('Erro ao carregar a lista de franqueados');
      } finally {
        setLoading(false);
      }
    };

    fetchFranqueados();
  }, []);

  const handleDelete = async (id: number) => {
    if (!window.confirm('Tem certeza que deseja excluir este franqueado?')) {
      return;
    }

    try {
      await franqueadoService.excluir(id);
      setFranqueados(franqueados.filter(franqueado => franqueado.id !== id));
    } catch (err) {
      console.error('Erro ao excluir franqueado:', err);
      setError('Erro ao excluir o franqueado');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#32bef0]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">{error}</h3>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-[#29306a]">Franqueados</h1>
          <p className="mt-2 text-sm text-[#64748b]">
            Lista de todos os franqueados cadastrados no sistema
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link
            to="/franqueados/novo"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-[#32bef0] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#2ba8d8] focus:outline-none focus:ring-2 focus:ring-[#32bef0] focus:ring-offset-2 sm:w-auto"
          >
            Adicionar franqueado
          </Link>
        </div>
      </div>

      <div className="mt-8 flex flex-col">
        <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-[#29306a] sm:pl-6"
                    >
                      Nome
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-[#29306a]"
                    >
                      Email
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-[#29306a]"
                    >
                      Usuários
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-[#29306a]"
                    >
                      Data Criação
                    </th>
                    <th
                      scope="col"
                      className="relative py-3.5 pl-3 pr-4 sm:pr-6"
                    >
                      <span className="sr-only">Ações</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {franqueados.map((franqueado) => (
                    <tr key={franqueado.id}>
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-[#29306a] sm:pl-6">
                        {franqueado.nome}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#64748b]">
                        {franqueado.email}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#64748b]">
                        {franqueado.usuarios.length} usuário(s)
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#64748b]">
                        {new Date(franqueado.created).toLocaleDateString('pt-BR')}
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <Link
                          to={`/franqueados/${franqueado.id}/editar`}
                          className="text-[#32bef0] hover:text-[#2ba8d8] mr-4"
                        >
                          Editar
                        </Link>
                        <button
                          onClick={() => handleDelete(franqueado.id)}
                          className="text-[#f55eeb] hover:text-[#e54dd8]"
                        >
                          Excluir
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 