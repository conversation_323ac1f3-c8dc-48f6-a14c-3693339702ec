{"name": "frontend-tact-new", "version": "0.1.0", "private": true, "proxy": "http://localhost:8000", "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/axios": "^0.14.4", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "lucide-react": "^0.539.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "react-select": "^5.10.2", "react-toastify": "^11.0.5", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "autoprefixer": "^10.4.14", "postcss": "^8.4.31", "tailwindcss": "^3.3.0"}}