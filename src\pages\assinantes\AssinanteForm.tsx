import React from 'react';
import { AssinanteFormFields } from '../../types/Assinante';
import { useForm, DefaultValues } from 'react-hook-form';

interface AssinanteFormProps {
  initialData?: AssinanteFormFields;
  onSubmit: (data: AssinanteFormFields) => void;
  isEdit?: boolean;
}

export const AssinanteForm: React.FC<AssinanteFormProps> = ({ initialData, onSubmit, isEdit }) => {
  const { register, handleSubmit, formState: { errors } } = useForm<AssinanteFormFields>({
    defaultValues: initialData as DefaultValues<AssinanteFormFields>,
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <label htmlFor="nome_completo" className="block text-sm font-medium text-gray-700">
          Nome Completo
        </label>
        <input
          type="text"
          id="nome_completo"
          {...register('nome_completo', { required: 'Nome completo é obrigatório' })}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
        {errors.nome_completo && <p className="mt-1 text-sm text-red-600">{errors.nome_completo.message}</p>}
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
          Email
        </label>
        <input
          type="email"
          id="email"
          {...register('email', {
            required: 'Email é obrigatório',
            pattern: {
              value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
              message: 'Email inválido',
            },
          })}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
        {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
      </div>

      {!isEdit && (
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            Senha
          </label>
          <input
            type="password"
            id="password"
            {...register('password', { required: 'Senha é obrigatória' })}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
          {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>}
        </div>
      )}

      <button
        type="submit"
        className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        {isEdit ? 'Salvar Alterações' : 'Cadastrar Assinante'}
      </button>
    </form>
  );
};