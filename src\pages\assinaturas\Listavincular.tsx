import React, { useEffect, useState } from 'react';
import { vinculacao } from '../../services/funcionarioService';
import { PaginatedTable, Column } from '../../components/Table/PaginatedTable';
import { Funcionario } from '../../types/Funcionario';

export const Listavincular: React.FC = () => {
  const [assinantes, setAssinantes] = useState<Funcionario[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [filters, setFilters] = useState({
    search: '',
    tipo: '',
    status:'',
  });

  // Modal de edição
  const [modalOpen, setModalOpen] = useState(false);
  const [funcionarioSelecionado, setFuncionarioSelecionado] = useState<Funcionario | null>(null);

  // Ações
  const handleAcao = async (id: number, acao: 'aceitar' | 'rejeitar') => {
    try {
      await vinculacao[acao](id);
      fetchListavincular();
      fecharModal();
    } catch (err) {
      console.error(`Erro ao ${acao}:`, err);
    }
  };

  const handleBloquear = async (id: number) => {
    try {
      //await vinculacao.bloquear(id);
      fetchListavincular();
    } catch (err) {
      console.error('Erro ao bloquear:', err);
    }
  };

  const abrirModalEditar = (record: Funcionario) => {
    setFuncionarioSelecionado(record);
    setModalOpen(true);
  };

  const fecharModal = () => {
    setModalOpen(false);
    setFuncionarioSelecionado(null);
  };

  // Buscar lista de assinantes com filtros e paginação
  const fetchListavincular = async () => {
    try {
      setLoading(true);
      const codigo = await vinculacao.meucodigo();
      const response = await vinculacao.listar(
        pageSize,
        (currentPage - 1) * pageSize,
        codigo,
        filters.status,
        filters.search
      );

      setAssinantes(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Erro ao buscar assinantes:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchListavincular();
  }, [currentPage, filters]);

  // Colunas para tabela
  const columns: Column<Funcionario>[] = [
    {
      key: 'nome_completo',
      title: 'Nome',
      render: (_, record) => (
        <span className="font-medium text-gray-900">{record.nome_completo}</span>
      ),
    },
    {
      key: 'email',
      title: 'Email',
      render: (_, record) => (
        <span>{record.email || 'Não informado'}</span>
      ),
    },
    {
      key: 'status_vinculo',
      title: 'Status',
      render: (_, record) => {
        if (record.status_vinculo === 'pendente') {
          return <span className="text-yellow-700 font-semibold">Pendente</span>;
        }
        if (record.status_vinculo === 'aceito') {
          return <span className="text-green-700 font-semibold">Aceito</span>;
        }
        if (record.status_vinculo === 'rejeitado') {
          return <span className="text-red-700 font-semibold">Rejeitado</span>;
        }
        return <span>-</span>;
      },
    },
    {
      key: 'id',
      title: 'Ações',
      render: (_, record) => (
        <div className="flex space-x-2">
          {record.status_vinculo === 'pendente' && (
            <>
              <button
                onClick={() => handleAcao(record.id, 'aceitar')}
                className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
              >
                Aceitar
              </button>
              <button
                onClick={() => handleAcao(record.id, 'rejeitar')}
                className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
              >
                Rejeitar
              </button>
            </>
          )}

          {record.status_vinculo === 'aceito' && (
            <>
              <button
                onClick={() => abrirModalEditar(record)}
                className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
              >
                Editar
              </button>
              <button
                onClick={() => handleBloquear(record.id)}
                className="px-3 py-1 bg-gray-700 text-white rounded hover:bg-gray-800 text-sm"
              >
                Bloquear
              </button>
            </>
          )}

          {record.status_vinculo === 'rejeitado' && (
            <button
              onClick={() => abrirModalEditar(record)}
              className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
            >
              Editar
            </button>
          )}
        </div>
      ),
    },
  ];

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setCurrentPage(1);
  };

  const limparFiltros = () => {
    setFilters({ search: '', tipo: '', status: '' });
    setCurrentPage(1);
  };

  // Filtra rejeitados apenas quando o filtro está ativo
  const dataFiltrada = assinantes.filter(a => {
    if (filters.status === 'rejeitado') return true;
    return a.status_vinculo !== 'rejeitado';
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Solicitação de cadastro</h1>
      </div>

      {/* Filtros */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-lg font-semibold mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome do usuário
            </label>
            <input
              type="text"
              placeholder="Digite o nome..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status do vínculo
            </label>
            <select
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">Todos</option>
              <option value="pendente">Pendentes</option>
              <option value="aceito">Aceitos</option>
              <option value="rejeitado">Rejeitados</option>
            </select>
          </div>
        </div>

        <div className="mt-4">
          <button
            onClick={limparFiltros}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Limpar Filtros
          </button>
        </div>
      </div>

      {/* Tabela */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <PaginatedTable
          data={dataFiltrada}
          columns={columns}
          loading={loading}
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / pageSize)}
          onPageChange={setCurrentPage}
        />
      </div>

      {/* Modal de edição */}
      {modalOpen && funcionarioSelecionado && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-96">
            <h2 className="text-lg font-bold mb-4">Editar vínculo</h2>
            <p className="mb-4">Usuário: {funcionarioSelecionado.nome_completo}</p>

            <div className="flex space-x-2">
              <button
                onClick={() => handleAcao(funcionarioSelecionado.id, 'aceitar')}
                className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
              >
                Aceitar
              </button>
              <button
                onClick={() => handleAcao(funcionarioSelecionado.id, 'rejeitar')}
                className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
              >
                Rejeitar
              </button>
              <button
                onClick={fecharModal}
                className="px-3 py-1 bg-gray-400 text-white rounded hover:bg-gray-500 text-sm"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
