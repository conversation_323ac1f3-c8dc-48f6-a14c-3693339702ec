import React, { useState, useEffect } from 'react';
import { Empreendimento } from '../../types/empreendimento';
import { Incorporadora } from '../../types/incorporadora';
import { incorporadoraService } from '../../services/incorporadoraService';
import { cidadeService } from '../../services/cidadeService';

interface EmpreendimentoEditFormProps {
  empreendimento: Empreendimento;
  onSave: (data: Partial<Empreendimento>) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const EmpreendimentoEditForm: React.FC<EmpreendimentoEditFormProps> = ({
  empreendimento,
  onSave,
  onCancel,
  loading = false
}) => {
  const [formData, setFormData] = useState<Partial<Empreendimento>>({
  nome: empreendimento.nome,
  data_entrega: empreendimento.data_entrega,
  data_inicio_comercializacao: empreendimento.data_inicio_comercializacao,
  observacao: empreendimento.observacao,
  // Novos campos adicionados
  area_total: empreendimento.area_total,
  area_construida: empreendimento.area_construida,
  is_lancamento: empreendimento.is_lancamento,
});

  const [incorporadoras, setIncorporadoras] = useState<Incorporadora[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    carregarDados();
  }, []);

  const carregarDados = async () => {
    try {
      const incorporadorasData = await incorporadoraService.listarParaSelecao();
      setIncorporadoras(incorporadorasData);
    } catch (err) {
      console.error('Erro ao carregar incorporadoras:', err);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Limpar erro do campo
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.nome || formData.nome.trim() === '') {
      newErrors.nome = 'Nome é obrigatório';
    }
    
    // Validação para area_total
    if (formData.area_total !== undefined && formData.area_total < 0) {
      newErrors.area_total = 'A área total deve ser maior ou igual a zero.';
    }

    // Validação para area_construida
    if (formData.area_construida !== undefined && formData.area_construida < 0) {
      newErrors.area_construida = 'A área construída deve ser maior ou igual a zero.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Informações Básicas */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Editar Informações Básicas</h3>
        </div>
        <div className="border-t border-gray-200">
          <div className="px-4 py-5 space-y-6">
            {/* Nome */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Nome do Empreendimento *
              </label>
              <input
                type="text"
                value={formData.nome || ''}
                onChange={(e) => handleInputChange('nome', e.target.value)}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.nome ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.nome && <p className="mt-1 text-sm text-red-600">{errors.nome}</p>}
            </div>

            {/* Área Total */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Área Total (m²)
              </label>
              <input
                type="number"
                min="0"
                value={formData.area_total || ''}
                onChange={(e) => handleInputChange('area_total', e.target.value ? parseInt(e.target.value) : undefined)}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.area_total ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.area_total && <p className="mt-1 text-sm text-red-600">{errors.area_total}</p>}
            </div>
            {/* Área Construída */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Área Construída (m²)
              </label>
              <input
                type="number"
                min="0"
                value={formData.area_construida || ''}
                onChange={(e) => handleInputChange('area_construida', e.target.value ? parseInt(e.target.value) : undefined)}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.area_construida ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.area_construida && <p className="mt-1 text-sm text-red-600">{errors.area_construida}</p>}
            </div>

            {/* É Lançamento */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_lancamento"
                  name="is_lancamento"
                  checked={formData.is_lancamento}
                  onChange={(e) => handleInputChange('is_lancamento', e.target.checked)}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="is_lancamento" className="ml-2 block text-sm font-medium text-gray-700">
                  É Lançamento?
                </label>
              </div>

            {/* Data de Entrega */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Data de Entrega
              </label>
              <input
                type="date"
                value={formData.data_entrega ? formData.data_entrega.split('T')[0] : ''}
                onChange={(e) => handleInputChange('data_entrega', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Data de Início da Comercialização */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Data de Início da Comercialização
              </label>
              <input
                type="date"
                value={formData.data_inicio_comercializacao ? formData.data_inicio_comercializacao.split('T')[0] : ''}
                onChange={(e) => handleInputChange('data_inicio_comercializacao', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Observações */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Observações
              </label>
              <textarea
                rows={3}
                value={formData.observacao || ''}
                onChange={(e) => handleInputChange('observacao', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Botões */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          disabled={loading}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          Cancelar
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? 'Salvando...' : 'Salvar'}
        </button>
      </div>
    </form>
  );
};

export default EmpreendimentoEditForm; 